<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <!-- Permissions required for the app functionality -->
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.PACKAGE_USAGE_STATS" 
        tools:ignore="ProtectedPermissions" />
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES" 
        tools:ignore="QueryAllPackagesPermission" />

    <application
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.KidsPhone"
        tools:targetApi="31">
        
        <!-- Splash Activity -->
        <activity
            android:name=".activities.SplashActivity"
            android:exported="true"
            android:theme="@style/Theme.KidsPhone.Splash">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <!-- Onboarding Activity -->
        <activity
            android:name=".activities.OnboardingActivity"
            android:exported="false"
            android:screenOrientation="portrait" />

        <!-- Main Activity -->
        <activity
            android:name=".activities.MainActivity"
            android:exported="false"
            android:screenOrientation="portrait" />

        <!-- Child Mode Activity -->
        <activity
            android:name=".activities.ChildModeActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:launchMode="singleTask" />

        <!-- Settings Activity -->
        <activity
            android:name=".activities.SettingsActivity"
            android:exported="false"
            android:screenOrientation="portrait" />

        <!-- PIN Activity -->
        <activity
            android:name=".activities.PinActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.KidsPhone.Dialog" />

        <!-- App Monitor Service -->
        <service
            android:name=".services.AppMonitorService"
            android:enabled="true"
            android:exported="false" />

        <!-- Accessibility Service -->
        <service
            android:name=".services.KidsPhoneAccessibilityService"
            android:exported="false"
            android:permission="android.permission.BIND_ACCESSIBILITY_SERVICE">
            <intent-filter>
                <action android:name="android.accessibilityservice.AccessibilityService" />
            </intent-filter>
            <meta-data
                android:name="android.accessibilityservice"
                android:resource="@xml/accessibility_service_config" />
        </service>

        <!-- Boot Receiver -->
        <receiver
            android:name=".receivers.BootReceiver"
            android:enabled="true"
            android:exported="false">
            <intent-filter android:priority="1000">
                <action android:name="android.intent.action.BOOT_COMPLETED" />
                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
                <action android:name="android.intent.action.PACKAGE_REPLACED" />
                <data android:scheme="package" />
            </intent-filter>
        </receiver>

    </application>

</manifest>
