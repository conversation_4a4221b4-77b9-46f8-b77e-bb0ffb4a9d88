<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?attr/colorSurface"
    tools:context=".activities.MainActivity">

    <com.google.android.material.appbar.MaterialToolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="?attr/colorSurface"
        android:elevation="0dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:title="@string/app_name"
        app:titleTextColor="?attr/colorOnSurface">

        <com.google.android.material.button.MaterialButton
            android:id="@+id/themeToggleButton"
            style="@style/Widget.Material3.Button.IconButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="end"
            android:layout_marginEnd="8dp"
            android:contentDescription="@string/theme_toggle"
            app:icon="@drawable/ic_sun" />

    </com.google.android.material.appbar.MaterialToolbar>

    <ImageView
        android:id="@+id/logoImageView"
        android:layout_width="120dp"
        android:layout_height="120dp"
        android:layout_marginTop="48dp"
        android:contentDescription="@string/app_name"
        android:scaleType="centerInside"
        android:src="@drawable/logo"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/toolbar" />

    <TextView
        android:id="@+id/welcomeTextView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp"
        android:text="@string/welcome_message"
        android:textColor="?attr/colorOnSurface"
        android:textSize="24sp"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/logoImageView" />

    <TextView
        android:id="@+id/statusTextView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:textColor="?attr/colorOnSurfaceVariant"
        android:textSize="16sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/welcomeTextView"
        tools:text="Ready to use" />

    <LinearLayout
        android:id="@+id/buttonsContainer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="32dp"
        android:layout_marginTop="48dp"
        android:orientation="vertical"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/statusTextView">

        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="16dp"
            app:cardElevation="4dp">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/childModeButton"
                style="@style/Widget.Material3.Button"
                android:layout_width="match_parent"
                android:layout_height="80dp"
                android:text="@string/child_mode"
                android:textSize="20sp"
                app:backgroundTint="@color/child_mode_primary"
                app:icon="@drawable/ic_child_mode"
                app:iconGravity="start"
                app:iconSize="32dp" />

        </com.google.android.material.card.MaterialCardView>

        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="16dp"
            app:cardElevation="4dp">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/settingsButton"
                style="@style/Widget.Material3.Button.TonalButton"
                android:layout_width="match_parent"
                android:layout_height="80dp"
                android:text="@string/settings"
                android:textSize="20sp"
                app:icon="@drawable/ic_settings"
                app:iconGravity="start"
                app:iconSize="32dp" />

        </com.google.android.material.card.MaterialCardView>

    </LinearLayout>

    <com.google.android.material.button.MaterialButton
        android:id="@+id/appInfoButton"
        style="@style/Widget.Material3.Button.TextButton"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="32dp"
        android:text="@string/app_info"
        android:textColor="?attr/colorOnSurfaceVariant"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
