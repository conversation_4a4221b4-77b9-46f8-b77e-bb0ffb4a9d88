<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/child_mode_secondary"
    tools:context=".activities.ChildModeActivity">

    <com.google.android.material.appbar.MaterialToolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/child_mode_primary"
        android:elevation="4dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:title="@string/child_mode_title"
        app:titleTextColor="@android:color/white">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="end"
            android:layout_marginEnd="8dp"
            android:orientation="horizontal">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/refreshButton"
                style="@style/Widget.Material3.Button.IconButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="8dp"
                android:contentDescription="@string/refresh"
                app:icon="@drawable/ic_refresh"
                app:iconTint="@android:color/white" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/exitButton"
                style="@style/Widget.Material3.Button.TextButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/exit_child_mode"
                android:textColor="@android:color/white"
                app:icon="@drawable/ic_exit"
                app:iconTint="@android:color/white" />

        </LinearLayout>

    </com.google.android.material.appbar.MaterialToolbar>

    <TextView
        android:id="@+id/appsCountTextView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="16dp"
        android:layout_marginTop="16dp"
        android:gravity="center"
        android:textColor="?attr/colorOnSurface"
        android:textSize="16sp"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/toolbar"
        tools:text="5 apps available" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/appsRecyclerView"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="16dp"
        android:clipToPadding="false"
        android:padding="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/appsCountTextView"
        tools:listitem="@layout/item_app_child_mode" />

    <ProgressBar
        android:id="@+id/progressBar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/appsRecyclerView"
        app:layout_constraintEnd_toEndOf="@+id/appsRecyclerView"
        app:layout_constraintStart_toStartOf="@+id/appsRecyclerView"
        app:layout_constraintTop_toTopOf="@+id/appsRecyclerView" />

    <!-- Empty State Layout -->
    <LinearLayout
        android:id="@+id/emptyStateLayout"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:gravity="center"
        android:orientation="vertical"
        android:padding="32dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/appsCountTextView">

        <ImageView
            android:layout_width="120dp"
            android:layout_height="120dp"
            android:layout_marginBottom="24dp"
            android:alpha="0.6"
            android:contentDescription="@string/no_apps_available"
            android:src="@drawable/ic_empty_apps"
            android:tint="?attr/colorOnSurfaceVariant" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:gravity="center"
            android:text="@string/no_allowed_apps"
            android:textColor="?attr/colorOnSurface"
            android:textSize="20sp"
            android:textStyle="bold" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="@string/no_allowed_apps_message"
            android:textColor="?attr/colorOnSurfaceVariant"
            android:textSize="16sp" />

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
