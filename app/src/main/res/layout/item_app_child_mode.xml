<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    android:clickable="true"
    android:focusable="true"
    app:cardCornerRadius="16dp"
    app:cardElevation="4dp"
    app:rippleColor="@color/child_mode_primary">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="16dp">

        <ImageView
            android:id="@+id/appIconImageView"
            android:layout_width="64dp"
            android:layout_height="64dp"
            android:contentDescription="@string/app_icon"
            android:scaleType="centerCrop"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:src="@drawable/ic_launcher_foreground" />

        <TextView
            android:id="@+id/appNameTextView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:ellipsize="end"
            android:gravity="center"
            android:maxLines="2"
            android:textColor="?attr/colorOnSurface"
            android:textSize="14sp"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/appIconImageView"
            tools:text="App Name" />

        <!-- Hidden elements not needed in child mode -->
        <TextView
            android:id="@+id/packageNameTextView"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:visibility="gone" />

        <TextView
            android:id="@+id/systemAppIndicator"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:visibility="gone" />

        <CheckBox
            android:id="@+id/appCheckBox"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:visibility="gone" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</com.google.android.material.card.MaterialCardView>
