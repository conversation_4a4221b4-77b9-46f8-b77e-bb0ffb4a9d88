<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?attr/colorSurface"
    android:padding="24dp"
    tools:context=".activities.PinActivity">

    <TextView
        android:id="@+id/titleTextView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="48dp"
        android:gravity="center"
        android:text="@string/enter_pin_title"
        android:textColor="?attr/colorOnSurface"
        android:textSize="24sp"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/attemptsTextView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:gravity="center"
        android:textColor="@color/pin_error"
        android:textSize="14sp"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/titleTextView"
        tools:text="4 attempts remaining"
        tools:visibility="visible" />

    <!-- Hidden EditText for PIN input -->
    <EditText
        android:id="@+id/pinEditText"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:alpha="0"
        android:inputType="numberPassword"
        android:maxLength="6"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/attemptsTextView"
        tools:ignore="LabelFor" />

    <!-- PIN Dots Display -->
    <LinearLayout
        android:id="@+id/pinDotsContainer"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="48dp"
        android:gravity="center"
        android:orientation="horizontal"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/attemptsTextView">

        <View
            android:id="@+id/dot1"
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:layout_margin="8dp"
            android:background="@drawable/pin_dot" />

        <View
            android:id="@+id/dot2"
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:layout_margin="8dp"
            android:background="@drawable/pin_dot" />

        <View
            android:id="@+id/dot3"
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:layout_margin="8dp"
            android:background="@drawable/pin_dot" />

        <View
            android:id="@+id/dot4"
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:layout_margin="8dp"
            android:background="@drawable/pin_dot" />

        <View
            android:id="@+id/dot5"
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:layout_margin="8dp"
            android:background="@drawable/pin_dot" />

        <View
            android:id="@+id/dot6"
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:layout_margin="8dp"
            android:background="@drawable/pin_dot" />

    </LinearLayout>

    <!-- Number Pad -->
    <GridLayout
        android:id="@+id/numberPad"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="48dp"
        android:columnCount="3"
        android:rowCount="4"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/pinDotsContainer">

        <com.google.android.material.button.MaterialButton
            android:id="@+id/button1"
            style="@style/Widget.Material3.Button.TextButton"
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:layout_margin="8dp"
            android:text="1"
            android:textSize="24sp" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/button2"
            style="@style/Widget.Material3.Button.TextButton"
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:layout_margin="8dp"
            android:text="2"
            android:textSize="24sp" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/button3"
            style="@style/Widget.Material3.Button.TextButton"
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:layout_margin="8dp"
            android:text="3"
            android:textSize="24sp" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/button4"
            style="@style/Widget.Material3.Button.TextButton"
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:layout_margin="8dp"
            android:text="4"
            android:textSize="24sp" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/button5"
            style="@style/Widget.Material3.Button.TextButton"
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:layout_margin="8dp"
            android:text="5"
            android:textSize="24sp" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/button6"
            style="@style/Widget.Material3.Button.TextButton"
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:layout_margin="8dp"
            android:text="6"
            android:textSize="24sp" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/button7"
            style="@style/Widget.Material3.Button.TextButton"
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:layout_margin="8dp"
            android:text="7"
            android:textSize="24sp" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/button8"
            style="@style/Widget.Material3.Button.TextButton"
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:layout_margin="8dp"
            android:text="8"
            android:textSize="24sp" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/button9"
            style="@style/Widget.Material3.Button.TextButton"
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:layout_margin="8dp"
            android:text="9"
            android:textSize="24sp" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/clearButton"
            style="@style/Widget.Material3.Button.TextButton"
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:layout_margin="8dp"
            android:text="@string/clear"
            android:textSize="16sp" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/button0"
            style="@style/Widget.Material3.Button.TextButton"
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:layout_margin="8dp"
            android:text="0"
            android:textSize="24sp" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/backspaceButton"
            style="@style/Widget.Material3.Button.TextButton"
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:layout_margin="8dp"
            android:text="⌫"
            android:textSize="20sp" />

    </GridLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
