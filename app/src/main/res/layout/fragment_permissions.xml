<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="16dp">

        <TextView
            android:id="@+id/titleTextView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="@string/permissions_title"
            android:textColor="?attr/colorOnSurface"
            android:textSize="24sp"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/descriptionTextView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:gravity="center"
            android:text="@string/permissions_description"
            android:textColor="?attr/colorOnSurfaceVariant"
            android:textSize="14sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/titleTextView" />

        <com.google.android.material.progressindicator.LinearProgressIndicator
            android:id="@+id/progressIndicator"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            android:progress="0"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/descriptionTextView" />

        <TextView
            android:id="@+id/progressText"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:gravity="center"
            android:textColor="?attr/colorOnSurfaceVariant"
            android:textSize="12sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/progressIndicator"
            tools:text="0 of 3 permissions granted" />

        <!-- Usage Stats Permission Card -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/usageStatsCard"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="32dp"
            android:clickable="true"
            android:focusable="true"
            app:cardCornerRadius="12dp"
            app:cardElevation="2dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/progressText">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:padding="16dp">

                <ImageView
                    android:id="@+id/usageStatsIcon"
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    android:contentDescription="@string/usage_stats_permission"
                    android:src="@drawable/ic_usage_stats"
                    android:tint="?attr/colorPrimary"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/usageStatsTitle"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_marginEnd="8dp"
                    android:text="@string/usage_stats_permission"
                    android:textColor="?attr/colorOnSurface"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    app:layout_constraintEnd_toStartOf="@+id/usageStatsButton"
                    app:layout_constraintStart_toEndOf="@+id/usageStatsIcon"
                    app:layout_constraintTop_toTopOf="@+id/usageStatsIcon" />

                <TextView
                    android:id="@+id/usageStatsDescription"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="4dp"
                    android:layout_marginEnd="8dp"
                    android:text="@string/usage_stats_description"
                    android:textColor="?attr/colorOnSurfaceVariant"
                    android:textSize="12sp"
                    app:layout_constraintEnd_toStartOf="@+id/usageStatsButton"
                    app:layout_constraintStart_toEndOf="@+id/usageStatsIcon"
                    app:layout_constraintTop_toBottomOf="@+id/usageStatsTitle" />

                <TextView
                    android:id="@+id/usageStatsStatus"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="4dp"
                    android:layout_marginEnd="8dp"
                    android:textSize="12sp"
                    android:textStyle="bold"
                    app:layout_constraintEnd_toStartOf="@+id/usageStatsButton"
                    app:layout_constraintStart_toEndOf="@+id/usageStatsIcon"
                    app:layout_constraintTop_toBottomOf="@+id/usageStatsDescription"
                    tools:text="Required" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/usageStatsButton"
                    style="@style/Widget.Material3.Button.TextButton"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/grant"
                    app:layout_constraintBottom_toBottomOf="@+id/usageStatsIcon"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="@+id/usageStatsIcon" />

            </androidx.constraintlayout.widget.ConstraintLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- Accessibility Permission Card -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/accessibilityCard"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:clickable="true"
            android:focusable="true"
            app:cardCornerRadius="12dp"
            app:cardElevation="2dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/usageStatsCard">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:padding="16dp">

                <ImageView
                    android:id="@+id/accessibilityIcon"
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    android:contentDescription="@string/accessibility_permission"
                    android:src="@drawable/ic_accessibility"
                    android:tint="?attr/colorPrimary"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/accessibilityTitle"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_marginEnd="8dp"
                    android:text="@string/accessibility_permission"
                    android:textColor="?attr/colorOnSurface"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    app:layout_constraintEnd_toStartOf="@+id/accessibilityButton"
                    app:layout_constraintStart_toEndOf="@+id/accessibilityIcon"
                    app:layout_constraintTop_toTopOf="@+id/accessibilityIcon" />

                <TextView
                    android:id="@+id/accessibilityDescription"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="4dp"
                    android:layout_marginEnd="8dp"
                    android:text="@string/accessibility_description"
                    android:textColor="?attr/colorOnSurfaceVariant"
                    android:textSize="12sp"
                    app:layout_constraintEnd_toStartOf="@+id/accessibilityButton"
                    app:layout_constraintStart_toEndOf="@+id/accessibilityIcon"
                    app:layout_constraintTop_toBottomOf="@+id/accessibilityTitle" />

                <TextView
                    android:id="@+id/accessibilityStatus"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="4dp"
                    android:layout_marginEnd="8dp"
                    android:textSize="12sp"
                    android:textStyle="bold"
                    app:layout_constraintEnd_toStartOf="@+id/accessibilityButton"
                    app:layout_constraintStart_toEndOf="@+id/accessibilityIcon"
                    app:layout_constraintTop_toBottomOf="@+id/accessibilityDescription"
                    tools:text="Required" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/accessibilityButton"
                    style="@style/Widget.Material3.Button.TextButton"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/grant"
                    app:layout_constraintBottom_toBottomOf="@+id/accessibilityIcon"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="@+id/accessibilityIcon" />

            </androidx.constraintlayout.widget.ConstraintLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- Overlay Permission Card -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/overlayCard"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:clickable="true"
            android:focusable="true"
            app:cardCornerRadius="12dp"
            app:cardElevation="2dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/accessibilityCard">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:padding="16dp">

                <ImageView
                    android:id="@+id/overlayIcon"
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    android:contentDescription="@string/overlay_permission"
                    android:src="@drawable/ic_overlay"
                    android:tint="?attr/colorPrimary"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/overlayTitle"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_marginEnd="8dp"
                    android:text="@string/overlay_permission"
                    android:textColor="?attr/colorOnSurface"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    app:layout_constraintEnd_toStartOf="@+id/overlayButton"
                    app:layout_constraintStart_toEndOf="@+id/overlayIcon"
                    app:layout_constraintTop_toTopOf="@+id/overlayIcon" />

                <TextView
                    android:id="@+id/overlayDescription"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="4dp"
                    android:layout_marginEnd="8dp"
                    android:text="@string/overlay_description"
                    android:textColor="?attr/colorOnSurfaceVariant"
                    android:textSize="12sp"
                    app:layout_constraintEnd_toStartOf="@+id/overlayButton"
                    app:layout_constraintStart_toEndOf="@+id/overlayIcon"
                    app:layout_constraintTop_toBottomOf="@+id/overlayTitle" />

                <TextView
                    android:id="@+id/overlayStatus"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="4dp"
                    android:layout_marginEnd="8dp"
                    android:textSize="12sp"
                    android:textStyle="bold"
                    app:layout_constraintEnd_toStartOf="@+id/overlayButton"
                    app:layout_constraintStart_toEndOf="@+id/overlayIcon"
                    app:layout_constraintTop_toBottomOf="@+id/overlayDescription"
                    tools:text="Required" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/overlayButton"
                    style="@style/Widget.Material3.Button.TextButton"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/grant"
                    app:layout_constraintBottom_toBottomOf="@+id/overlayIcon"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="@+id/overlayIcon" />

            </androidx.constraintlayout.widget.ConstraintLayout>

        </com.google.android.material.card.MaterialCardView>

        <com.google.android.material.button.MaterialButton
            android:id="@+id/grantAllButton"
            style="@style/Widget.Material3.Button"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="32dp"
            android:text="@string/grant_all_permissions"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/overlayCard" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</ScrollView>
