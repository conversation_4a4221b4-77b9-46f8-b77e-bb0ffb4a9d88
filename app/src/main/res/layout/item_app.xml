<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="4dp"
    android:clickable="true"
    android:focusable="true"
    app:cardCornerRadius="12dp"
    app:cardElevation="2dp"
    app:rippleColor="?attr/colorPrimary">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="16dp">

        <ImageView
            android:id="@+id/appIconImageView"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:contentDescription="@string/app_icon"
            android:scaleType="centerCrop"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:src="@drawable/ic_launcher_foreground" />

        <TextView
            android:id="@+id/appNameTextView"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="8dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="?attr/colorOnSurface"
            android:textSize="16sp"
            android:textStyle="bold"
            app:layout_constraintEnd_toStartOf="@+id/systemAppIndicator"
            app:layout_constraintStart_toEndOf="@+id/appIconImageView"
            app:layout_constraintTop_toTopOf="@+id/appIconImageView"
            tools:text="App Name" />

        <TextView
            android:id="@+id/packageNameTextView"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="4dp"
            android:layout_marginEnd="8dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="?attr/colorOnSurfaceVariant"
            android:textSize="12sp"
            app:layout_constraintEnd_toStartOf="@+id/systemAppIndicator"
            app:layout_constraintStart_toEndOf="@+id/appIconImageView"
            app:layout_constraintTop_toBottomOf="@+id/appNameTextView"
            tools:text="com.example.app" />

        <TextView
            android:id="@+id/systemAppIndicator"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="8dp"
            android:background="@drawable/system_app_badge"
            android:paddingHorizontal="8dp"
            android:paddingVertical="2dp"
            android:text="@string/system_app"
            android:textColor="?attr/colorOnSecondaryContainer"
            android:textSize="10sp"
            android:visibility="gone"
            app:layout_constraintEnd_toStartOf="@+id/appCheckBox"
            app:layout_constraintTop_toTopOf="@+id/appIconImageView"
            tools:visibility="visible" />

        <CheckBox
            android:id="@+id/appCheckBox"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:buttonTint="?attr/colorPrimary"
            app:layout_constraintBottom_toBottomOf="@+id/appIconImageView"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/appIconImageView" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</com.google.android.material.card.MaterialCardView>
