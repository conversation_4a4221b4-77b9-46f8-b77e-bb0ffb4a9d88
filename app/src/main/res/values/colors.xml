<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Light theme colors -->
    <color name="primary_light">#6750A4</color>
    <color name="on_primary_light">#FFFFFF</color>
    <color name="primary_container_light">#EADDFF</color>
    <color name="on_primary_container_light">#21005D</color>
    <color name="secondary_light">#625B71</color>
    <color name="on_secondary_light">#FFFFFF</color>
    <color name="secondary_container_light">#E8DEF8</color>
    <color name="on_secondary_container_light">#1D192B</color>
    <color name="tertiary_light">#7D5260</color>
    <color name="on_tertiary_light">#FFFFFF</color>
    <color name="tertiary_container_light">#FFD8E4</color>
    <color name="on_tertiary_container_light">#31111D</color>
    <color name="error_light">#BA1A1A</color>
    <color name="on_error_light">#FFFFFF</color>
    <color name="error_container_light">#FFDAD6</color>
    <color name="on_error_container_light">#410002</color>
    <color name="outline_light">#79747E</color>
    <color name="outline_variant_light">#CAC4D0</color>
    <color name="surface_light">#FFFBFE</color>
    <color name="on_surface_light">#1C1B1F</color>
    <color name="surface_variant_light">#E7E0EC</color>
    <color name="on_surface_variant_light">#49454F</color>
    <color name="surface_inverse_light">#313033</color>
    <color name="on_surface_inverse_light">#F4EFF4</color>
    <color name="primary_inverse_light">#D0BCFF</color>

    <!-- Dark theme colors -->
    <color name="primary_dark">#D0BCFF</color>
    <color name="on_primary_dark">#381E72</color>
    <color name="primary_container_dark">#4F378B</color>
    <color name="on_primary_container_dark">#EADDFF</color>
    <color name="secondary_dark">#CCC2DC</color>
    <color name="on_secondary_dark">#332D41</color>
    <color name="secondary_container_dark">#4A4458</color>
    <color name="on_secondary_container_dark">#E8DEF8</color>
    <color name="tertiary_dark">#EFB8C8</color>
    <color name="on_tertiary_dark">#492532</color>
    <color name="tertiary_container_dark">#633B48</color>
    <color name="on_tertiary_container_dark">#FFD8E4</color>
    <color name="error_dark">#FFB4AB</color>
    <color name="on_error_dark">#690005</color>
    <color name="error_container_dark">#93000A</color>
    <color name="on_error_container_dark">#FFDAD6</color>
    <color name="outline_dark">#938F99</color>
    <color name="outline_variant_dark">#49454F</color>
    <color name="surface_dark">#1C1B1F</color>
    <color name="on_surface_dark">#E6E1E5</color>
    <color name="surface_variant_dark">#49454F</color>
    <color name="on_surface_variant_dark">#CAC4D0</color>
    <color name="surface_inverse_dark">#E6E1E5</color>
    <color name="on_surface_inverse_dark">#313033</color>
    <color name="primary_inverse_dark">#6750A4</color>

    <!-- Additional colors -->
    <color name="child_mode_primary">#4CAF50</color>
    <color name="child_mode_secondary">#81C784</color>
    <color name="pin_error">#F44336</color>
    <color name="pin_success">#4CAF50</color>
</resources>
