<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="Theme.KidsPhone" parent="Theme.Material3.DayNight">
        <!-- Customize your light theme here. -->
        <item name="colorPrimary">@color/primary_light</item>
        <item name="colorOnPrimary">@color/on_primary_light</item>
        <item name="colorPrimaryContainer">@color/primary_container_light</item>
        <item name="colorOnPrimaryContainer">@color/on_primary_container_light</item>
        <item name="colorSecondary">@color/secondary_light</item>
        <item name="colorOnSecondary">@color/on_secondary_light</item>
        <item name="colorSecondaryContainer">@color/secondary_container_light</item>
        <item name="colorOnSecondaryContainer">@color/on_secondary_container_light</item>
        <item name="colorTertiary">@color/tertiary_light</item>
        <item name="colorOnTertiary">@color/on_tertiary_light</item>
        <item name="colorTertiaryContainer">@color/tertiary_container_light</item>
        <item name="colorOnTertiaryContainer">@color/on_tertiary_container_light</item>
        <item name="colorError">@color/error_light</item>
        <item name="colorOnError">@color/on_error_light</item>
        <item name="colorErrorContainer">@color/error_container_light</item>
        <item name="colorOnErrorContainer">@color/on_error_container_light</item>
        <item name="colorOutline">@color/outline_light</item>
        <item name="colorOutlineVariant">@color/outline_variant_light</item>
        <item name="colorSurface">@color/surface_light</item>
        <item name="colorOnSurface">@color/on_surface_light</item>
        <item name="colorSurfaceVariant">@color/surface_variant_light</item>
        <item name="colorOnSurfaceVariant">@color/on_surface_variant_light</item>
        <item name="colorSurfaceInverse">@color/surface_inverse_light</item>
        <item name="colorOnSurfaceInverse">@color/on_surface_inverse_light</item>
        <item name="colorPrimaryInverse">@color/primary_inverse_light</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:windowLightStatusBar">true</item>
    </style>

    <!-- Splash theme -->
    <style name="Theme.KidsPhone.Splash" parent="Theme.KidsPhone">
        <item name="android:windowBackground">@drawable/splash_background</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowActionBar">false</item>
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowContentOverlay">@null</item>
    </style>

    <!-- Dialog theme -->
    <style name="Theme.KidsPhone.Dialog" parent="Theme.Material3.DayNight.Dialog">
        <item name="colorPrimary">@color/primary_light</item>
        <item name="colorSecondary">@color/secondary_light</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowCloseOnTouchOutside">false</item>
    </style>

    <!-- Child Mode theme -->
    <style name="Theme.KidsPhone.ChildMode" parent="Theme.KidsPhone">
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowActionBar">false</item>
        <item name="android:windowFullscreen">false</item>
    </style>
</resources>
