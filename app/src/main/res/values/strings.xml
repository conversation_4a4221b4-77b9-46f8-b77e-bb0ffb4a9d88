<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- App Name -->
    <string name="app_name">KidsPhone</string>
    
    <!-- Splash Screen -->
    <string name="loading">Loading...</string>
    
    <!-- Onboarding -->
    <string name="welcome_title">Welcome to KidsPhone</string>
    <string name="setup_pin_title">Set Up PIN</string>
    <string name="setup_pin_description">Create a PIN to secure parental controls</string>
    <string name="enter_pin">Enter PIN</string>
    <string name="confirm_pin">Confirm PIN</string>
    <string name="pin_mismatch">PINs do not match</string>
    <string name="pin_too_short">PIN must be at least 4 digits</string>
    <string name="pin_setup_success">PIN set successfully</string>
    <string name="clear">Clear</string>
    <string name="app_icon">App Icon</string>
    <string name="system_app">System</string>
    <string name="search_apps">Search apps...</string>
    <string name="all_apps">All Apps</string>
    <string name="user_apps">User Apps</string>
    <string name="system_apps">System Apps</string>
    <string name="recommended">Recommended</string>
    <string name="selected_apps_count">%d apps selected</string>
    <string name="select_all">Select All</string>
    <string name="select_none">Select None</string>
    <string name="error_loading_apps">Error loading apps</string>
    <string name="permission_granted">Granted</string>
    <string name="permission_required">Required</string>
    <string name="grant">Grant</string>
    <string name="granted">Granted</string>
    <string name="permissions_progress">%d of %d permissions granted</string>
    <string name="grant_all_permissions">Grant All Permissions</string>
    <string name="error_opening_settings">Error opening settings</string>
    <string name="welcome_message">Welcome to KidsPhone</string>
    <string name="pin_not_set">Please set up PIN first</string>
    <string name="no_apps_configured">Please configure allowed apps first</string>
    <string name="enter_pin_for_settings">Enter PIN to access settings</string>
    <string name="light_theme">Light Theme</string>
    <string name="dark_theme">Dark Theme</string>
    <string name="system_theme">System Theme</string>
    <string name="app_info_message">KidsPhone v1.0\nParental control app for Android\nDeveloped with care for families</string>
    <string name="setup_required_pin">Setup required: Set PIN</string>
    <string name="setup_required_apps">Setup required: Configure apps</string>
    <string name="ready_to_use">Ready to use</string>
    
    <string name="select_apps_title">Select Allowed Apps</string>
    <string name="select_apps_description">Choose which apps your child can use</string>
    <string name="no_apps_selected">Please select at least one app</string>
    
    <string name="permissions_title">Required Permissions</string>
    <string name="permissions_description">Grant permissions for parental controls to work</string>
    <string name="usage_stats_permission">Usage Statistics</string>
    <string name="usage_stats_description">Monitor app usage</string>
    <string name="accessibility_permission">Accessibility Service</string>
    <string name="accessibility_description">Control app access</string>
    <string name="overlay_permission">Display Over Other Apps</string>
    <string name="overlay_description">Show PIN screen</string>
    
    <!-- Main Screen -->
    <string name="main_title">KidsPhone</string>
    <string name="child_mode">Child Mode</string>
    <string name="settings">Settings</string>
    <string name="theme_toggle">Toggle Theme</string>
    
    <!-- Child Mode -->
    <string name="child_mode_title">Child Mode</string>
    <string name="exit_child_mode">Exit</string>
    <string name="no_allowed_apps">No allowed apps configured</string>
    
    <!-- PIN Screen -->
    <string name="enter_pin_title">Enter PIN</string>
    <string name="wrong_pin">Wrong PIN</string>
    <string name="pin_attempts_remaining">Attempts remaining: %d</string>
    
    <!-- Settings -->
    <string name="settings_title">Settings</string>
    <string name="change_pin">Change PIN</string>
    <string name="manage_apps">Manage Allowed Apps</string>
    <string name="app_info">App Information</string>
    <string name="about">About</string>
    
    <!-- General -->
    <string name="next">Next</string>
    <string name="back">Back</string>
    <string name="finish">Finish</string>
    <string name="cancel">Cancel</string>
    <string name="ok">OK</string>
    <string name="yes">Yes</string>
    <string name="no">No</string>
    <string name="save">Save</string>
    <string name="delete">Delete</string>
    <string name="edit">Edit</string>
    
    <!-- Error Messages -->
    <string name="error_generic">An error occurred</string>
    <string name="error_permission_denied">Permission denied</string>
    <string name="error_app_not_found">App not found</string>
    
    <!-- Accessibility Service -->
    <string name="accessibility_service_description">KidsPhone Accessibility Service monitors app launches to enforce parental controls</string>
</resources>
