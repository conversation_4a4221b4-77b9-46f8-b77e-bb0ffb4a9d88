<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme for dark mode. -->
    <style name="Theme.KidsPhone" parent="Theme.Material3.DayNight">
        <!-- Customize your dark theme here. -->
        <item name="colorPrimary">@color/primary_dark</item>
        <item name="colorOnPrimary">@color/on_primary_dark</item>
        <item name="colorPrimaryContainer">@color/primary_container_dark</item>
        <item name="colorOnPrimaryContainer">@color/on_primary_container_dark</item>
        <item name="colorSecondary">@color/secondary_dark</item>
        <item name="colorOnSecondary">@color/on_secondary_dark</item>
        <item name="colorSecondaryContainer">@color/secondary_container_dark</item>
        <item name="colorOnSecondaryContainer">@color/on_secondary_container_dark</item>
        <item name="colorTertiary">@color/tertiary_dark</item>
        <item name="colorOnTertiary">@color/on_tertiary_dark</item>
        <item name="colorTertiaryContainer">@color/tertiary_container_dark</item>
        <item name="colorOnTertiaryContainer">@color/on_tertiary_container_dark</item>
        <item name="colorError">@color/error_dark</item>
        <item name="colorOnError">@color/on_error_dark</item>
        <item name="colorErrorContainer">@color/error_container_dark</item>
        <item name="colorOnErrorContainer">@color/on_error_container_dark</item>
        <item name="colorOutline">@color/outline_dark</item>
        <item name="colorOutlineVariant">@color/outline_variant_dark</item>
        <item name="colorSurface">@color/surface_dark</item>
        <item name="colorOnSurface">@color/on_surface_dark</item>
        <item name="colorSurfaceVariant">@color/surface_variant_dark</item>
        <item name="colorOnSurfaceVariant">@color/on_surface_variant_dark</item>
        <item name="colorSurfaceInverse">@color/surface_inverse_dark</item>
        <item name="colorOnSurfaceInverse">@color/on_surface_inverse_dark</item>
        <item name="colorPrimaryInverse">@color/primary_inverse_dark</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:windowLightStatusBar">false</item>
    </style>

    <!-- Splash theme for dark mode -->
    <style name="Theme.KidsPhone.Splash" parent="Theme.KidsPhone">
        <item name="android:windowBackground">@drawable/splash_background_dark</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowActionBar">false</item>
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowContentOverlay">@null</item>
    </style>
</resources>
