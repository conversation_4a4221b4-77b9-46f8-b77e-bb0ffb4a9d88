package com.kidsphone.activities

import android.content.Intent
import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.Fragment
import androidx.viewpager2.adapter.FragmentStateAdapter
import androidx.viewpager2.widget.ViewPager2
import com.google.android.material.tabs.TabLayoutMediator
import com.kidsphone.R
import com.kidsphone.databinding.ActivityOnboardingBinding
import com.kidsphone.fragments.onboarding.PinSetupFragment
import com.kidsphone.fragments.onboarding.AppSelectionFragment
import com.kidsphone.fragments.onboarding.PermissionsFragment
import com.kidsphone.utils.PreferencesManager

class OnboardingActivity : AppCompatActivity() {
    
    private lateinit var binding: ActivityOnboardingBinding
    private lateinit var preferencesManager: PreferencesManager
    private lateinit var onboardingAdapter: OnboardingAdapter
    
    companion object {
        const val TOTAL_PAGES = 3
        const val PAGE_PIN_SETUP = 0
        const val PAGE_APP_SELECTION = 1
        const val PAGE_PERMISSIONS = 2
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityOnboardingBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        preferencesManager = PreferencesManager(this)
        
        setupViewPager()
        setupNavigation()
    }
    
    private fun setupViewPager() {
        onboardingAdapter = OnboardingAdapter(this)
        binding.viewPager.adapter = onboardingAdapter
        binding.viewPager.isUserInputEnabled = false // Disable swipe navigation
        
        // Setup tab indicator
        TabLayoutMediator(binding.tabLayout, binding.viewPager) { _, _ ->
            // Empty implementation - just for dots indicator
        }.attach()
        
        // Listen for page changes
        binding.viewPager.registerOnPageChangeCallback(object : ViewPager2.OnPageChangeCallback() {
            override fun onPageSelected(position: Int) {
                super.onPageSelected(position)
                updateNavigationButtons(position)
            }
        })
    }
    
    private fun setupNavigation() {
        binding.backButton.setOnClickListener {
            if (binding.viewPager.currentItem > 0) {
                binding.viewPager.currentItem = binding.viewPager.currentItem - 1
            }
        }
        
        binding.nextButton.setOnClickListener {
            handleNextButtonClick()
        }
        
        updateNavigationButtons(0)
    }
    
    private fun updateNavigationButtons(position: Int) {
        binding.backButton.isEnabled = position > 0
        
        when (position) {
            PAGE_PIN_SETUP -> {
                binding.nextButton.text = getString(R.string.next)
                binding.nextButton.isEnabled = isPinSetupComplete()
            }
            PAGE_APP_SELECTION -> {
                binding.nextButton.text = getString(R.string.next)
                binding.nextButton.isEnabled = isAppSelectionComplete()
            }
            PAGE_PERMISSIONS -> {
                binding.nextButton.text = getString(R.string.finish)
                binding.nextButton.isEnabled = arePermissionsGranted()
            }
        }
    }
    
    private fun handleNextButtonClick() {
        val currentPosition = binding.viewPager.currentItem
        
        when (currentPosition) {
            PAGE_PIN_SETUP -> {
                if (isPinSetupComplete()) {
                    binding.viewPager.currentItem = PAGE_APP_SELECTION
                }
            }
            PAGE_APP_SELECTION -> {
                if (isAppSelectionComplete()) {
                    binding.viewPager.currentItem = PAGE_PERMISSIONS
                }
            }
            PAGE_PERMISSIONS -> {
                if (arePermissionsGranted()) {
                    completeOnboarding()
                }
            }
        }
    }
    
    private fun isPinSetupComplete(): Boolean {
        return preferencesManager.hasPinSet()
    }
    
    private fun isAppSelectionComplete(): Boolean {
        return preferencesManager.getAllowedApps().isNotEmpty()
    }
    
    private fun arePermissionsGranted(): Boolean {
        val permissionsFragment = onboardingAdapter.createFragment(PAGE_PERMISSIONS) as? PermissionsFragment
        return permissionsFragment?.areAllPermissionsGranted() ?: false
    }
    
    private fun completeOnboarding() {
        preferencesManager.setFirstTimeSetupComplete()
        
        val intent = Intent(this, MainActivity::class.java)
        startActivity(intent)
        finish()
        
        overridePendingTransition(R.anim.fade_in, R.anim.fade_out)
    }
    
    fun refreshNavigationButtons() {
        updateNavigationButtons(binding.viewPager.currentItem)
    }
    
    override fun onBackPressed() {
        if (binding.viewPager.currentItem > 0) {
            binding.viewPager.currentItem = binding.viewPager.currentItem - 1
        } else {
            super.onBackPressed()
        }
    }
    
    private inner class OnboardingAdapter(activity: OnboardingActivity) : FragmentStateAdapter(activity) {
        
        override fun getItemCount(): Int = TOTAL_PAGES
        
        override fun createFragment(position: Int): Fragment {
            return when (position) {
                PAGE_PIN_SETUP -> PinSetupFragment()
                PAGE_APP_SELECTION -> AppSelectionFragment()
                PAGE_PERMISSIONS -> PermissionsFragment()
                else -> throw IllegalArgumentException("Invalid position: $position")
            }
        }
    }
}
