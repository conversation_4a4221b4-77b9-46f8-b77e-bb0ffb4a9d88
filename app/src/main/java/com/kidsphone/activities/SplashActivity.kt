package com.kidsphone.activities

import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.animation.AnimationUtils
import androidx.appcompat.app.AppCompatActivity
import com.kidsphone.R
import com.kidsphone.databinding.ActivitySplashBinding
import com.kidsphone.utils.PreferencesManager

class SplashActivity : AppCompatActivity() {
    
    private lateinit var binding: ActivitySplashBinding
    private lateinit var preferencesManager: PreferencesManager
    
    companion object {
        private const val SPLASH_DELAY = 3000L // 3 seconds
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivitySplashBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        preferencesManager = PreferencesManager(this)
        
        setupAnimation()
        navigateAfterDelay()
    }
    
    private fun setupAnimation() {
        // Load fade in animation for logo
        val fadeInAnimation = AnimationUtils.loadAnimation(this, R.anim.fade_in)
        binding.logoImageView.startAnimation(fadeInAnimation)
        
        // Load slide up animation for loading text
        val slideUpAnimation = AnimationUtils.loadAnimation(this, R.anim.slide_up)
        binding.loadingTextView.startAnimation(slideUpAnimation)
    }
    
    private fun navigateAfterDelay() {
        Handler(Looper.getMainLooper()).postDelayed({
            navigateToNextScreen()
        }, SPLASH_DELAY)
    }
    
    private fun navigateToNextScreen() {
        val intent = if (preferencesManager.isFirstTimeSetup()) {
            Intent(this, OnboardingActivity::class.java)
        } else {
            Intent(this, MainActivity::class.java)
        }
        
        startActivity(intent)
        finish()
        
        // Add transition animation
        overridePendingTransition(R.anim.fade_in, R.anim.fade_out)
    }
    
    override fun onBackPressed() {
        // Disable back button on splash screen
        // Do nothing
    }
}
