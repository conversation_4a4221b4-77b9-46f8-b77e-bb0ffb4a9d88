package com.kidsphone.activities

import android.content.Intent
import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.app.AppCompatDelegate
import com.google.android.material.dialog.MaterialAlertDialogBuilder
import com.kidsphone.R
import com.kidsphone.databinding.ActivityMainBinding
import com.kidsphone.utils.PreferencesManager

class MainActivity : AppCompatActivity() {
    
    private lateinit var binding: ActivityMainBinding
    private lateinit var preferencesManager: PreferencesManager
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityMainBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        preferencesManager = PreferencesManager(this)
        
        setupViews()
        applyTheme()
    }
    
    private fun setupViews() {
        // Child Mode Button
        binding.childModeButton.setOnClickListener {
            startChildMode()
        }
        
        // Settings Button
        binding.settingsButton.setOnClickListener {
            openSettings()
        }
        
        // Theme Toggle Button
        binding.themeToggleButton.setOnClickListener {
            toggleTheme()
        }
        
        // App Info Button
        binding.appInfoButton.setOnClickListener {
            showAppInfo()
        }
        
        updateThemeIcon()
    }
    
    private fun startChildMode() {
        // Check if PIN is set and apps are configured
        if (!preferencesManager.hasPinSet()) {
            showError(getString(R.string.pin_not_set))
            return
        }
        
        if (preferencesManager.getAllowedApps().isEmpty()) {
            showError(getString(R.string.no_apps_configured))
            return
        }
        
        // Start child mode
        val intent = Intent(this, ChildModeActivity::class.java)
        startActivity(intent)
        
        // Set child mode as active
        preferencesManager.setChildModeActive(true)
    }
    
    private fun openSettings() {
        // Require PIN to access settings
        val intent = Intent(this, PinActivity::class.java).apply {
            putExtra(PinActivity.EXTRA_MODE, PinActivity.MODE_VERIFY)
            putExtra(PinActivity.EXTRA_TITLE, getString(R.string.enter_pin_for_settings))
        }
        startActivityForResult(intent, REQUEST_PIN_FOR_SETTINGS)
    }
    
    private fun toggleTheme() {
        val currentTheme = preferencesManager.getThemeMode()
        val newTheme = when (currentTheme) {
            PreferencesManager.THEME_LIGHT -> PreferencesManager.THEME_DARK
            PreferencesManager.THEME_DARK -> PreferencesManager.THEME_SYSTEM
            else -> PreferencesManager.THEME_LIGHT
        }
        
        preferencesManager.setThemeMode(newTheme)
        applyTheme()
        updateThemeIcon()
    }
    
    private fun applyTheme() {
        when (preferencesManager.getThemeMode()) {
            PreferencesManager.THEME_LIGHT -> {
                AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_NO)
            }
            PreferencesManager.THEME_DARK -> {
                AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_YES)
            }
            PreferencesManager.THEME_SYSTEM -> {
                AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_FOLLOW_SYSTEM)
            }
        }
    }
    
    private fun updateThemeIcon() {
        val iconRes = when (preferencesManager.getThemeMode()) {
            PreferencesManager.THEME_LIGHT -> R.drawable.ic_sun
            PreferencesManager.THEME_DARK -> R.drawable.ic_moon
            else -> R.drawable.ic_theme_auto
        }
        binding.themeToggleButton.setIconResource(iconRes)
        
        val contentDescription = when (preferencesManager.getThemeMode()) {
            PreferencesManager.THEME_LIGHT -> getString(R.string.light_theme)
            PreferencesManager.THEME_DARK -> getString(R.string.dark_theme)
            else -> getString(R.string.system_theme)
        }
        binding.themeToggleButton.contentDescription = contentDescription
    }
    
    private fun showAppInfo() {
        MaterialAlertDialogBuilder(this)
            .setTitle(getString(R.string.app_info))
            .setMessage(getString(R.string.app_info_message))
            .setPositiveButton(getString(R.string.ok)) { dialog, _ ->
                dialog.dismiss()
            }
            .show()
    }
    
    private fun showError(message: String) {
        MaterialAlertDialogBuilder(this)
            .setTitle(getString(R.string.error_generic))
            .setMessage(message)
            .setPositiveButton(getString(R.string.ok)) { dialog, _ ->
                dialog.dismiss()
            }
            .show()
    }
    
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        
        when (requestCode) {
            REQUEST_PIN_FOR_SETTINGS -> {
                if (resultCode == RESULT_OK) {
                    // PIN verified, open settings
                    val intent = Intent(this, SettingsActivity::class.java)
                    startActivity(intent)
                }
            }
        }
    }
    
    override fun onResume() {
        super.onResume()
        
        // If child mode was active and we're back here, deactivate it
        if (preferencesManager.isChildModeActive()) {
            preferencesManager.setChildModeActive(false)
        }
        
        // Update UI based on current configuration
        updateButtonStates()
    }
    
    private fun updateButtonStates() {
        val hasPinSet = preferencesManager.hasPinSet()
        val hasAppsConfigured = preferencesManager.getAllowedApps().isNotEmpty()
        
        binding.childModeButton.isEnabled = hasPinSet && hasAppsConfigured
        
        if (!hasPinSet) {
            binding.statusTextView.text = getString(R.string.setup_required_pin)
        } else if (!hasAppsConfigured) {
            binding.statusTextView.text = getString(R.string.setup_required_apps)
        } else {
            binding.statusTextView.text = getString(R.string.ready_to_use)
        }
    }
    
    companion object {
        private const val REQUEST_PIN_FOR_SETTINGS = 1001
    }
}
