package com.kidsphone.activities

import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.GridLayoutManager
import com.google.android.material.dialog.MaterialAlertDialogBuilder
import com.kidsphone.R
import com.kidsphone.adapters.AppsAdapter
import com.kidsphone.databinding.ActivityChildModeBinding
import com.kidsphone.models.AppInfo
import com.kidsphone.utils.AppUtils
import com.kidsphone.utils.PreferencesManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class ChildModeActivity : AppCompatActivity() {
    
    private lateinit var binding: ActivityChildModeBinding
    private lateinit var preferencesManager: PreferencesManager
    private lateinit var appUtils: AppUtils
    private lateinit var appsAdapter: AppsAdapter
    
    private var allowedApps = listOf<AppInfo>()
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityChildModeBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        preferencesManager = PreferencesManager(this)
        appUtils = AppUtils(this)
        
        setupViews()
        loadAllowedApps()
        
        // Set child mode as active
        preferencesManager.setChildModeActive(true)
    }
    
    private fun setupViews() {
        // Setup RecyclerView with grid layout
        val spanCount = calculateSpanCount()
        appsAdapter = AppsAdapter(
            onAppClick = { app ->
                launchApp(app)
            },
            showCheckbox = false
        )
        
        binding.appsRecyclerView.apply {
            layoutManager = GridLayoutManager(this@ChildModeActivity, spanCount)
            adapter = appsAdapter
        }
        
        // Setup exit button
        binding.exitButton.setOnClickListener {
            showExitDialog()
        }
        
        // Setup refresh button
        binding.refreshButton.setOnClickListener {
            loadAllowedApps()
        }
    }
    
    private fun calculateSpanCount(): Int {
        val displayMetrics = resources.displayMetrics
        val screenWidthDp = displayMetrics.widthPixels / displayMetrics.density
        return (screenWidthDp / 120).toInt().coerceAtLeast(2) // Minimum 2 columns
    }
    
    private fun loadAllowedApps() {
        binding.progressBar.visibility = View.VISIBLE
        binding.appsRecyclerView.visibility = View.GONE
        binding.emptyStateLayout.visibility = View.GONE
        
        lifecycleScope.launch {
            try {
                val allowedPackages = preferencesManager.getAllowedApps()
                
                if (allowedPackages.isEmpty()) {
                    showEmptyState()
                    return@launch
                }
                
                val allApps = withContext(Dispatchers.IO) {
                    appUtils.getInstalledApps()
                }
                
                allowedApps = allApps.filter { app ->
                    allowedPackages.contains(app.packageName)
                }
                
                if (allowedApps.isEmpty()) {
                    showEmptyState()
                } else {
                    appsAdapter.submitList(allowedApps)
                    binding.appsRecyclerView.visibility = View.VISIBLE
                    binding.appsCountTextView.text = getString(R.string.apps_available, allowedApps.size)
                }
                
            } catch (e: Exception) {
                showError(getString(R.string.error_loading_apps))
            } finally {
                binding.progressBar.visibility = View.GONE
            }
        }
    }
    
    private fun showEmptyState() {
        binding.emptyStateLayout.visibility = View.VISIBLE
        binding.appsRecyclerView.visibility = View.GONE
        binding.appsCountTextView.text = getString(R.string.no_apps_available)
    }
    
    private fun launchApp(app: AppInfo) {
        val success = appUtils.launchApp(app.packageName)
        if (!success) {
            showError(getString(R.string.error_launching_app, app.appName))
        }
    }
    
    private fun showExitDialog() {
        MaterialAlertDialogBuilder(this)
            .setTitle(getString(R.string.exit_child_mode))
            .setMessage(getString(R.string.exit_child_mode_message))
            .setPositiveButton(getString(R.string.yes)) { _, _ ->
                requestPinToExit()
            }
            .setNegativeButton(getString(R.string.no)) { dialog, _ ->
                dialog.dismiss()
            }
            .setCancelable(false)
            .show()
    }
    
    private fun requestPinToExit() {
        val intent = Intent(this, PinActivity::class.java).apply {
            putExtra(PinActivity.EXTRA_MODE, PinActivity.MODE_VERIFY)
            putExtra(PinActivity.EXTRA_TITLE, getString(R.string.enter_pin_to_exit))
        }
        startActivityForResult(intent, REQUEST_PIN_TO_EXIT)
    }
    
    private fun exitChildMode() {
        preferencesManager.setChildModeActive(false)
        finish()
    }
    
    private fun showError(message: String) {
        MaterialAlertDialogBuilder(this)
            .setTitle(getString(R.string.error_generic))
            .setMessage(message)
            .setPositiveButton(getString(R.string.ok)) { dialog, _ ->
                dialog.dismiss()
            }
            .show()
    }
    
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        
        when (requestCode) {
            REQUEST_PIN_TO_EXIT -> {
                if (resultCode == RESULT_OK) {
                    exitChildMode()
                }
            }
        }
    }
    
    override fun onBackPressed() {
        // Disable back button in child mode - must use exit button
        showExitDialog()
    }
    
    override fun onResume() {
        super.onResume()
        
        // Check if we're still supposed to be in child mode
        if (!preferencesManager.isChildModeActive()) {
            finish()
            return
        }
        
        // Refresh apps in case permissions changed
        loadAllowedApps()
    }
    
    override fun onPause() {
        super.onPause()
        
        // If we're pausing and child mode is still active, it means the user
        // is trying to access another app. We'll handle this in the service.
    }
    
    override fun onDestroy() {
        super.onDestroy()
        
        // Only deactivate child mode if we're actually finishing
        if (isFinishing) {
            preferencesManager.setChildModeActive(false)
        }
    }
    
    companion object {
        private const val REQUEST_PIN_TO_EXIT = 2001
    }
}
