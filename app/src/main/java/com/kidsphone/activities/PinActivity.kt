package com.kidsphone.activities

import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import androidx.appcompat.app.AppCompatActivity
import com.google.android.material.snackbar.Snackbar
import com.kidsphone.R
import com.kidsphone.databinding.ActivityPinBinding
import com.kidsphone.utils.PreferencesManager
import com.kidsphone.utils.SecurityUtils

class PinActivity : AppCompatActivity() {
    
    private lateinit var binding: ActivityPinBinding
    private lateinit var preferencesManager: PreferencesManager
    private lateinit var securityUtils: SecurityUtils
    
    private var mode = MODE_VERIFY
    private var title = ""
    private var attempts = 0
    private val maxAttempts = 5
    
    companion object {
        const val EXTRA_MODE = "mode"
        const val EXTRA_TITLE = "title"
        const val MODE_VERIFY = "verify"
        const val MODE_CHANGE = "change"
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityPinBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        preferencesManager = PreferencesManager(this)
        securityUtils = SecurityUtils()
        
        mode = intent.getStringExtra(EXTRA_MODE) ?: MODE_VERIFY
        title = intent.getStringExtra(EXTRA_TITLE) ?: getString(R.string.enter_pin_title)
        
        setupViews()
        setupPinInput()
    }
    
    private fun setupViews() {
        binding.titleTextView.text = title
        
        binding.clearButton.setOnClickListener {
            clearPin()
        }
        
        // Setup number pad
        setupNumberPad()
        
        updateAttemptsDisplay()
    }
    
    private fun setupNumberPad() {
        val numberButtons = listOf(
            binding.button0, binding.button1, binding.button2, binding.button3,
            binding.button4, binding.button5, binding.button6, binding.button7,
            binding.button8, binding.button9
        )
        
        numberButtons.forEachIndexed { index, button ->
            button.setOnClickListener {
                addDigit(index.toString())
            }
        }
        
        binding.backspaceButton.setOnClickListener {
            removeLastDigit()
        }
    }
    
    private fun setupPinInput() {
        binding.pinEditText.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
            
            override fun afterTextChanged(s: Editable?) {
                val pin = s?.toString() ?: ""
                updatePinDots(pin.length)
                
                if (pin.length >= 4) {
                    verifyPin(pin)
                }
            }
        })
    }
    
    private fun addDigit(digit: String) {
        val currentPin = binding.pinEditText.text.toString()
        if (currentPin.length < 6) { // Max 6 digits
            binding.pinEditText.setText(currentPin + digit)
        }
    }
    
    private fun removeLastDigit() {
        val currentPin = binding.pinEditText.text.toString()
        if (currentPin.isNotEmpty()) {
            binding.pinEditText.setText(currentPin.dropLast(1))
        }
    }
    
    private fun clearPin() {
        binding.pinEditText.setText("")
    }
    
    private fun updatePinDots(length: Int) {
        val dots = listOf(
            binding.dot1, binding.dot2, binding.dot3,
            binding.dot4, binding.dot5, binding.dot6
        )
        
        dots.forEachIndexed { index, dot ->
            dot.isSelected = index < length
        }
    }
    
    private fun verifyPin(pin: String) {
        val storedHash = preferencesManager.getPinHash()
        val storedSalt = preferencesManager.getPinSalt()
        
        if (storedHash == null || storedSalt == null) {
            showError(getString(R.string.pin_not_set))
            return
        }
        
        if (securityUtils.verifyPin(pin, storedHash, storedSalt)) {
            // PIN correct
            setResult(RESULT_OK)
            finish()
        } else {
            // PIN incorrect
            attempts++
            binding.pinEditText.setText("")
            
            if (attempts >= maxAttempts) {
                showError(getString(R.string.too_many_attempts))
                setResult(RESULT_CANCELED)
                finish()
            } else {
                showError(getString(R.string.wrong_pin))
                updateAttemptsDisplay()
            }
        }
    }
    
    private fun updateAttemptsDisplay() {
        val remainingAttempts = maxAttempts - attempts
        if (attempts > 0) {
            binding.attemptsTextView.text = getString(R.string.pin_attempts_remaining, remainingAttempts)
            binding.attemptsTextView.visibility = android.view.View.VISIBLE
        } else {
            binding.attemptsTextView.visibility = android.view.View.GONE
        }
    }
    
    private fun showError(message: String) {
        Snackbar.make(binding.root, message, Snackbar.LENGTH_LONG)
            .setBackgroundTint(resources.getColor(R.color.pin_error, null))
            .show()
    }
    
    override fun onBackPressed() {
        setResult(RESULT_CANCELED)
        super.onBackPressed()
    }
}
