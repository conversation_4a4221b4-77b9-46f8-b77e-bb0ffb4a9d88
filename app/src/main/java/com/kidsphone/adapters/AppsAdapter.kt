package com.kidsphone.adapters

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.kidsphone.databinding.ItemAppBinding
import com.kidsphone.models.AppInfo

class AppsAdapter(
    private val onAppClick: (AppInfo) -> Unit,
    private val showCheckbox: Boolean = true
) : ListAdapter<AppInfo, AppsAdapter.AppViewHolder>(AppDiffCallback()) {
    
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): AppViewHolder {
        val binding = ItemAppBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return AppViewHolder(binding)
    }
    
    override fun onBindViewHolder(holder: AppViewHolder, position: Int) {
        holder.bind(getItem(position))
    }
    
    inner class AppViewHolder(
        private val binding: ItemAppBinding
    ) : RecyclerView.ViewHolder(binding.root) {
        
        fun bind(app: AppInfo) {
            binding.appNameTextView.text = app.appName
            binding.packageNameTextView.text = app.packageName
            binding.appIconImageView.setImageDrawable(app.icon)
            
            if (showCheckbox) {
                binding.appCheckBox.isChecked = app.isSelected
                binding.appCheckBox.setOnCheckedChangeListener { _, isChecked ->
                    app.isSelected = isChecked
                    onAppClick(app)
                }
            } else {
                binding.appCheckBox.visibility = android.view.View.GONE
            }
            
            // Show system app indicator
            if (app.isSystemApp) {
                binding.systemAppIndicator.visibility = android.view.View.VISIBLE
            } else {
                binding.systemAppIndicator.visibility = android.view.View.GONE
            }
            
            binding.root.setOnClickListener {
                if (showCheckbox) {
                    binding.appCheckBox.isChecked = !binding.appCheckBox.isChecked
                } else {
                    onAppClick(app)
                }
            }
        }
    }
    
    private class AppDiffCallback : DiffUtil.ItemCallback<AppInfo>() {
        override fun areItemsTheSame(oldItem: AppInfo, newItem: AppInfo): Boolean {
            return oldItem.packageName == newItem.packageName
        }
        
        override fun areContentsTheSame(oldItem: AppInfo, newItem: AppInfo): Boolean {
            return oldItem == newItem && oldItem.isSelected == newItem.isSelected
        }
    }
}
