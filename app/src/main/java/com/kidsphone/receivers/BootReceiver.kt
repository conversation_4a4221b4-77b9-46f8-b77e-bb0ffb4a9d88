package com.kidsphone.receivers

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import com.kidsphone.services.AppMonitorService
import com.kidsphone.utils.PreferencesManager

class BootReceiver : BroadcastReceiver() {
    
    override fun onReceive(context: Context, intent: Intent) {
        when (intent.action) {
            Intent.ACTION_BOOT_COMPLETED,
            Intent.ACTION_MY_PACKAGE_REPLACED,
            Intent.ACTION_PACKAGE_REPLACED -> {
                handleBootCompleted(context)
            }
        }
    }
    
    private fun handleBootCompleted(context: Context) {
        val preferencesManager = PreferencesManager(context)
        
        // Only start the service if child mode was active before reboot
        if (preferencesManager.isChildModeActive() && 
            preferencesManager.hasPinSet() &&
            preferencesManager.getAllowedApps().isNotEmpty()) {
            
            AppMonitorService.startService(context)
        }
    }
}
