package com.kidsphone.services

import android.app.Service
import android.content.Context
import android.content.Intent
import android.graphics.PixelFormat
import android.os.Build
import android.os.IBinder
import android.provider.Settings
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.WindowManager
import android.widget.Toast
import com.kidsphone.R
import com.kidsphone.activities.ChildModeActivity
import com.kidsphone.databinding.OverlayPinBinding
import com.kidsphone.utils.PreferencesManager
import com.kidsphone.utils.SecurityUtils

class OverlayService : Service() {
    
    private lateinit var windowManager: WindowManager
    private lateinit var preferencesManager: PreferencesManager
    private lateinit var securityUtils: SecurityUtils
    
    private var overlayView: View? = null
    private var binding: OverlayPinBinding? = null
    private var blockedAppPackage: String? = null
    
    companion object {
        private const val EXTRA_BLOCKED_APP = "blocked_app"
        
        fun showOverlay(context: Context, blockedAppPackage: String) {
            if (!Settings.canDrawOverlays(context)) {
                // Fallback to child mode if overlay permission not granted
                val intent = Intent(context, ChildModeActivity::class.java).apply {
                    flags = Intent.FLAG_ACTIVITY_NEW_TASK
                }
                context.startActivity(intent)
                return
            }
            
            val intent = Intent(context, OverlayService::class.java).apply {
                putExtra(EXTRA_BLOCKED_APP, blockedAppPackage)
            }
            context.startService(intent)
        }
        
        fun hideOverlay(context: Context) {
            val intent = Intent(context, OverlayService::class.java)
            context.stopService(intent)
        }
    }
    
    override fun onCreate() {
        super.onCreate()
        
        windowManager = getSystemService(Context.WINDOW_SERVICE) as WindowManager
        preferencesManager = PreferencesManager(this)
        securityUtils = SecurityUtils()
    }
    
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        blockedAppPackage = intent?.getStringExtra(EXTRA_BLOCKED_APP)
        
        if (overlayView == null) {
            createOverlay()
        }
        
        return START_NOT_STICKY
    }
    
    override fun onBind(intent: Intent?): IBinder? {
        return null
    }
    
    override fun onDestroy() {
        super.onDestroy()
        removeOverlay()
    }
    
    private fun createOverlay() {
        try {
            binding = OverlayPinBinding.inflate(LayoutInflater.from(this))
            overlayView = binding?.root
            
            setupOverlayView()
            addOverlayToWindow()
            
        } catch (e: Exception) {
            // Fallback to child mode if overlay creation fails
            redirectToChildMode()
        }
    }
    
    private fun setupOverlayView() {
        binding?.let { binding ->
            // Set blocked app info
            blockedAppPackage?.let { packageName ->
                val appName = getAppName(packageName)
                binding.blockedAppTextView.text = getString(R.string.app_blocked_message, appName)
            }
            
            // Setup PIN input
            setupPinInput(binding)
            
            // Setup buttons
            binding.cancelButton.setOnClickListener {
                redirectToChildMode()
            }
            
            binding.emergencyButton.setOnClickListener {
                showEmergencyDialog()
            }
        }
    }
    
    private fun setupPinInput(binding: OverlayPinBinding) {
        // Setup number pad
        val numberButtons = listOf(
            binding.button0, binding.button1, binding.button2, binding.button3,
            binding.button4, binding.button5, binding.button6, binding.button7,
            binding.button8, binding.button9
        )
        
        numberButtons.forEachIndexed { index, button ->
            button.setOnClickListener {
                addDigit(index.toString())
            }
        }
        
        binding.backspaceButton.setOnClickListener {
            removeLastDigit()
        }
        
        binding.clearButton.setOnClickListener {
            clearPin()
        }
    }
    
    private fun addDigit(digit: String) {
        binding?.let { binding ->
            val currentPin = binding.pinEditText.text.toString()
            if (currentPin.length < 6) {
                binding.pinEditText.setText(currentPin + digit)
                updatePinDots(currentPin.length + 1)
                
                if (currentPin.length + 1 >= 4) {
                    verifyPin(currentPin + digit)
                }
            }
        }
    }
    
    private fun removeLastDigit() {
        binding?.let { binding ->
            val currentPin = binding.pinEditText.text.toString()
            if (currentPin.isNotEmpty()) {
                val newPin = currentPin.dropLast(1)
                binding.pinEditText.setText(newPin)
                updatePinDots(newPin.length)
            }
        }
    }
    
    private fun clearPin() {
        binding?.let { binding ->
            binding.pinEditText.setText("")
            updatePinDots(0)
        }
    }
    
    private fun updatePinDots(length: Int) {
        binding?.let { binding ->
            val dots = listOf(
                binding.dot1, binding.dot2, binding.dot3,
                binding.dot4, binding.dot5, binding.dot6
            )
            
            dots.forEachIndexed { index, dot ->
                dot.isSelected = index < length
            }
        }
    }
    
    private fun verifyPin(pin: String) {
        val storedHash = preferencesManager.getPinHash()
        val storedSalt = preferencesManager.getPinSalt()
        
        if (storedHash == null || storedSalt == null) {
            showError(getString(R.string.pin_not_set))
            return
        }
        
        if (securityUtils.verifyPin(pin, storedHash, storedSalt)) {
            // PIN correct - allow access and remove overlay
            preferencesManager.setChildModeActive(false)
            removeOverlay()
            stopSelf()
        } else {
            // PIN incorrect
            showError(getString(R.string.wrong_pin))
            clearPin()
        }
    }
    
    private fun addOverlayToWindow() {
        val layoutParams = WindowManager.LayoutParams(
            WindowManager.LayoutParams.MATCH_PARENT,
            WindowManager.LayoutParams.MATCH_PARENT,
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
            } else {
                @Suppress("DEPRECATION")
                WindowManager.LayoutParams.TYPE_PHONE
            },
            WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or
            WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL or
            WindowManager.LayoutParams.FLAG_WATCH_OUTSIDE_TOUCH,
            PixelFormat.TRANSLUCENT
        )
        
        layoutParams.gravity = Gravity.CENTER
        
        overlayView?.let { view ->
            windowManager.addView(view, layoutParams)
        }
    }
    
    private fun removeOverlay() {
        overlayView?.let { view ->
            try {
                windowManager.removeView(view)
            } catch (e: Exception) {
                // View might already be removed
            }
        }
        overlayView = null
        binding = null
    }
    
    private fun redirectToChildMode() {
        val intent = Intent(this, ChildModeActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP
        }
        startActivity(intent)
        
        removeOverlay()
        stopSelf()
    }
    
    private fun showEmergencyDialog() {
        // In a real implementation, this could show emergency contacts
        // or allow calling emergency services
        Toast.makeText(this, getString(R.string.emergency_feature_placeholder), Toast.LENGTH_SHORT).show()
    }
    
    private fun showError(message: String) {
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show()
    }
    
    private fun getAppName(packageName: String): String {
        return try {
            val applicationInfo = packageManager.getApplicationInfo(packageName, 0)
            packageManager.getApplicationLabel(applicationInfo).toString()
        } catch (e: Exception) {
            packageName
        }
    }
}
