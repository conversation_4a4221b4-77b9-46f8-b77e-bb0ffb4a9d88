package com.kidsphone.services

import android.accessibilityservice.AccessibilityService
import android.content.Intent
import android.view.accessibility.AccessibilityEvent
import com.kidsphone.activities.ChildModeActivity
import com.kidsphone.utils.PreferencesManager

class KidsPhoneAccessibilityService : AccessibilityService() {
    
    private lateinit var preferencesManager: PreferencesManager
    
    override fun onServiceConnected() {
        super.onServiceConnected()
        preferencesManager = PreferencesManager(this)
    }
    
    override fun onAccessibilityEvent(event: AccessibilityEvent?) {
        event?.let { handleAccessibilityEvent(it) }
    }
    
    override fun onInterrupt() {
        // Handle service interruption
    }
    
    private fun handleAccessibilityEvent(event: AccessibilityEvent) {
        // Only process if child mode is active
        if (!preferencesManager.isChildModeActive()) {
            return
        }
        
        when (event.eventType) {
            AccessibilityEvent.TYPE_WINDOW_STATE_CHANGED -> {
                handleWindowStateChanged(event)
            }
        }
    }
    
    private fun handleWindowStateChanged(event: AccessibilityEvent) {
        val packageName = event.packageName?.toString() ?: return
        
        // Skip our own app and system UI
        if (packageName == this.packageName || 
            packageName == "com.android.systemui" ||
            packageName == "android") {
            return
        }
        
        val allowedApps = preferencesManager.getAllowedApps()
        
        // If the app is not allowed, redirect to child mode
        if (!allowedApps.contains(packageName)) {
            redirectToChildMode(packageName)
        }
    }
    
    private fun redirectToChildMode(blockedPackageName: String) {
        try {
            val intent = Intent(this, ChildModeActivity::class.java).apply {
                flags = Intent.FLAG_ACTIVITY_NEW_TASK or 
                       Intent.FLAG_ACTIVITY_CLEAR_TOP or
                       Intent.FLAG_ACTIVITY_SINGLE_TOP
                putExtra("blocked_app", blockedPackageName)
            }
            
            startActivity(intent)
            
        } catch (e: Exception) {
            // Handle any errors silently
        }
    }
    
    companion object {
        /**
         * Check if the accessibility service is enabled
         */
        fun isServiceEnabled(preferencesManager: PreferencesManager): Boolean {
            // This is a simplified check - in a real implementation, you would
            // check if the accessibility service is actually enabled in system settings
            return false // Placeholder for now
        }
    }
}
