package com.kidsphone.services

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.app.Service
import android.app.usage.UsageEvents
import android.app.usage.UsageStatsManager
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.Handler
import android.os.IBinder
import android.os.Looper
import androidx.core.app.NotificationCompat
import com.kidsphone.R
import com.kidsphone.activities.ChildModeActivity
import com.kidsphone.activities.MainActivity
import com.kidsphone.utils.PreferencesManager
import java.util.concurrent.TimeUnit

class AppMonitorService : Service() {
    
    private lateinit var preferencesManager: PreferencesManager
    private lateinit var usageStatsManager: UsageStatsManager
    private lateinit var handler: Handler
    private lateinit var monitoringRunnable: Runnable
    
    private var isMonitoring = false
    private var lastCheckedTime = System.currentTimeMillis()
    
    companion object {
        private const val NOTIFICATION_ID = 1001
        private const val CHANNEL_ID = "app_monitor_channel"
        private const val MONITORING_INTERVAL = 2000L // 2 seconds
        
        fun startService(context: Context) {
            val intent = Intent(context, AppMonitorService::class.java)
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                context.startForegroundService(intent)
            } else {
                context.startService(intent)
            }
        }
        
        fun stopService(context: Context) {
            val intent = Intent(context, AppMonitorService::class.java)
            context.stopService(intent)
        }
    }
    
    override fun onCreate() {
        super.onCreate()
        
        preferencesManager = PreferencesManager(this)
        usageStatsManager = getSystemService(Context.USAGE_STATS_SERVICE) as UsageStatsManager
        handler = Handler(Looper.getMainLooper())
        
        createNotificationChannel()
        setupMonitoringRunnable()
    }
    
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        startForeground(NOTIFICATION_ID, createNotification())
        startMonitoring()
        return START_STICKY // Restart if killed
    }
    
    override fun onBind(intent: Intent?): IBinder? {
        return null // This is not a bound service
    }
    
    override fun onDestroy() {
        super.onDestroy()
        stopMonitoring()
    }
    
    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                getString(R.string.app_monitor_channel_name),
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = getString(R.string.app_monitor_channel_description)
                setShowBadge(false)
            }
            
            val notificationManager = getSystemService(NotificationManager::class.java)
            notificationManager.createNotificationChannel(channel)
        }
    }
    
    private fun createNotification(): Notification {
        val intent = Intent(this, MainActivity::class.java)
        val pendingIntent = PendingIntent.getActivity(
            this, 0, intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        
        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle(getString(R.string.app_monitor_notification_title))
            .setContentText(getString(R.string.app_monitor_notification_text))
            .setSmallIcon(R.drawable.ic_child_mode)
            .setContentIntent(pendingIntent)
            .setOngoing(true)
            .setSilent(true)
            .build()
    }
    
    private fun setupMonitoringRunnable() {
        monitoringRunnable = object : Runnable {
            override fun run() {
                if (isMonitoring) {
                    checkAppUsage()
                    handler.postDelayed(this, MONITORING_INTERVAL)
                }
            }
        }
    }
    
    private fun startMonitoring() {
        if (!isMonitoring) {
            isMonitoring = true
            lastCheckedTime = System.currentTimeMillis()
            handler.post(monitoringRunnable)
        }
    }
    
    private fun stopMonitoring() {
        isMonitoring = false
        handler.removeCallbacks(monitoringRunnable)
    }
    
    private fun checkAppUsage() {
        try {
            // Only monitor if child mode is active
            if (!preferencesManager.isChildModeActive()) {
                return
            }
            
            val currentTime = System.currentTimeMillis()
            val events = usageStatsManager.queryEvents(
                lastCheckedTime,
                currentTime
            )
            
            val allowedApps = preferencesManager.getAllowedApps()
            val ourPackageName = packageName
            
            while (events.hasNextEvent()) {
                val event = UsageEvents.Event()
                events.getNextEvent(event)
                
                // Check for app launch events
                if (event.eventType == UsageEvents.Event.MOVE_TO_FOREGROUND) {
                    val packageName = event.packageName
                    
                    // Skip our own app and system UI
                    if (packageName == ourPackageName || 
                        packageName == "com.android.systemui" ||
                        packageName == "android") {
                        continue
                    }
                    
                    // If the app is not in the allowed list, redirect to child mode
                    if (!allowedApps.contains(packageName)) {
                        redirectToChildMode(packageName)
                        break
                    }
                }
            }
            
            lastCheckedTime = currentTime
            
        } catch (e: Exception) {
            // Handle any errors silently to avoid crashes
        }
    }
    
    private fun redirectToChildMode(blockedPackageName: String) {
        try {
            // Create intent to launch child mode
            val intent = Intent(this, ChildModeActivity::class.java).apply {
                flags = Intent.FLAG_ACTIVITY_NEW_TASK or 
                       Intent.FLAG_ACTIVITY_CLEAR_TOP or
                       Intent.FLAG_ACTIVITY_SINGLE_TOP
                putExtra("blocked_app", blockedPackageName)
            }
            
            startActivity(intent)
            
        } catch (e: Exception) {
            // Handle any errors silently
        }
    }
    
    /**
     * Check if the service should be running based on child mode status
     */
    private fun shouldServiceRun(): Boolean {
        return preferencesManager.isChildModeActive() && 
               preferencesManager.hasPinSet() &&
               preferencesManager.getAllowedApps().isNotEmpty()
    }
    
    /**
     * Update monitoring status based on preferences
     */
    fun updateMonitoringStatus() {
        if (shouldServiceRun()) {
            if (!isMonitoring) {
                startMonitoring()
            }
        } else {
            if (isMonitoring) {
                stopMonitoring()
            }
        }
    }
}
