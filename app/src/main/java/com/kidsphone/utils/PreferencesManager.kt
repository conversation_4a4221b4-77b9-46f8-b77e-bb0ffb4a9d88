package com.kidsphone.utils

import android.content.Context
import android.content.SharedPreferences
import androidx.security.crypto.EncryptedSharedPreferences
import androidx.security.crypto.MasterKey

class PreferencesManager(private val context: Context) {
    
    companion object {
        private const val PREFS_NAME = "kidsphone_prefs"
        private const val SECURE_PREFS_NAME = "kidsphone_secure_prefs"
        
        // Keys for regular preferences
        private const val KEY_FIRST_TIME_SETUP = "first_time_setup"
        private const val KEY_CHILD_MODE_ACTIVE = "child_mode_active"
        private const val KEY_ALLOWED_APPS = "allowed_apps"
        private const val KEY_THEME_MODE = "theme_mode"
        private const val KEY_LANGUAGE = "language"
        
        // Keys for secure preferences
        private const val KEY_PIN_HASH = "pin_hash"
        private const val KEY_PIN_SALT = "pin_salt"
        
        // Theme modes
        const val THEME_SYSTEM = 0
        const val THEME_LIGHT = 1
        const val THEME_DARK = 2
    }
    
    private val regularPrefs: SharedPreferences by lazy {
        context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
    }
    
    private val securePrefs: SharedPreferences by lazy {
        val masterKey = MasterKey.Builder(context)
            .setKeyScheme(MasterKey.KeyScheme.AES256_GCM)
            .build()
            
        EncryptedSharedPreferences.create(
            context,
            SECURE_PREFS_NAME,
            masterKey,
            EncryptedSharedPreferences.PrefKeyEncryptionScheme.AES256_SIV,
            EncryptedSharedPreferences.PrefValueEncryptionScheme.AES256_GCM
        )
    }
    
    // First time setup
    fun isFirstTimeSetup(): Boolean {
        return regularPrefs.getBoolean(KEY_FIRST_TIME_SETUP, true)
    }
    
    fun setFirstTimeSetupComplete() {
        regularPrefs.edit().putBoolean(KEY_FIRST_TIME_SETUP, false).apply()
    }
    
    // Child mode
    fun isChildModeActive(): Boolean {
        return regularPrefs.getBoolean(KEY_CHILD_MODE_ACTIVE, false)
    }
    
    fun setChildModeActive(active: Boolean) {
        regularPrefs.edit().putBoolean(KEY_CHILD_MODE_ACTIVE, active).apply()
    }
    
    // Allowed apps
    fun getAllowedApps(): Set<String> {
        return regularPrefs.getStringSet(KEY_ALLOWED_APPS, emptySet()) ?: emptySet()
    }
    
    fun setAllowedApps(apps: Set<String>) {
        regularPrefs.edit().putStringSet(KEY_ALLOWED_APPS, apps).apply()
    }
    
    fun addAllowedApp(packageName: String) {
        val currentApps = getAllowedApps().toMutableSet()
        currentApps.add(packageName)
        setAllowedApps(currentApps)
    }
    
    fun removeAllowedApp(packageName: String) {
        val currentApps = getAllowedApps().toMutableSet()
        currentApps.remove(packageName)
        setAllowedApps(currentApps)
    }
    
    fun isAppAllowed(packageName: String): Boolean {
        return getAllowedApps().contains(packageName)
    }
    
    // Theme
    fun getThemeMode(): Int {
        return regularPrefs.getInt(KEY_THEME_MODE, THEME_SYSTEM)
    }
    
    fun setThemeMode(mode: Int) {
        regularPrefs.edit().putInt(KEY_THEME_MODE, mode).apply()
    }
    
    // Language
    fun getLanguage(): String {
        return regularPrefs.getString(KEY_LANGUAGE, "system") ?: "system"
    }
    
    fun setLanguage(language: String) {
        regularPrefs.edit().putString(KEY_LANGUAGE, language).apply()
    }
    
    // PIN management (secure)
    fun setPinHash(hash: String, salt: String) {
        securePrefs.edit()
            .putString(KEY_PIN_HASH, hash)
            .putString(KEY_PIN_SALT, salt)
            .apply()
    }
    
    fun getPinHash(): String? {
        return securePrefs.getString(KEY_PIN_HASH, null)
    }
    
    fun getPinSalt(): String? {
        return securePrefs.getString(KEY_PIN_SALT, null)
    }
    
    fun hasPinSet(): Boolean {
        return getPinHash() != null && getPinSalt() != null
    }
    
    fun clearPin() {
        securePrefs.edit()
            .remove(KEY_PIN_HASH)
            .remove(KEY_PIN_SALT)
            .apply()
    }
    
    // Clear all data
    fun clearAllData() {
        regularPrefs.edit().clear().apply()
        securePrefs.edit().clear().apply()
    }
}
