package com.kidsphone.utils

import java.security.MessageDigest
import java.security.SecureRandom
import javax.crypto.spec.PBEKeySpec
import javax.crypto.SecretKeyFactory
import android.util.Base64

class SecurityUtils {
    
    companion object {
        private const val PBKDF2_ALGORITHM = "PBKDF2WithHmacSHA256"
        private const val PBKDF2_ITERATIONS = 10000
        private const val SALT_LENGTH = 32
        private const val HASH_LENGTH = 256
    }
    
    /**
     * Generates a random salt for PIN hashing
     */
    fun generateSalt(): String {
        val random = SecureRandom()
        val salt = ByteArray(SALT_LENGTH)
        random.nextBytes(salt)
        return Base64.encodeToString(salt, Base64.NO_WRAP)
    }
    
    /**
     * Hashes a PIN using PBKDF2 with the provided salt
     */
    fun hashPin(pin: String, salt: String): String {
        val saltBytes = Base64.decode(salt, Base64.NO_WRAP)
        val spec = PBEKeySpec(pin.toCharArray(), saltBytes, PBKDF2_ITERATIONS, HASH_LENGTH)
        val factory = SecretKeyFactory.getInstance(PBKDF2_ALGORITHM)
        val hash = factory.generateSecret(spec).encoded
        return Base64.encodeToString(hash, Base64.NO_WRAP)
    }
    
    /**
     * Verifies a PIN against a stored hash and salt
     */
    fun verifyPin(pin: String, storedHash: String, salt: String): Boolean {
        val computedHash = hashPin(pin, salt)
        return computedHash == storedHash
    }
    
    /**
     * Generates a simple hash for non-critical data
     */
    fun simpleHash(input: String): String {
        val digest = MessageDigest.getInstance("SHA-256")
        val hash = digest.digest(input.toByteArray())
        return Base64.encodeToString(hash, Base64.NO_WRAP)
    }
    
    /**
     * Generates a random token for session management
     */
    fun generateToken(): String {
        val random = SecureRandom()
        val token = ByteArray(32)
        random.nextBytes(token)
        return Base64.encodeToString(token, Base64.NO_WRAP)
    }
    
    /**
     * Validates PIN format (digits only, minimum length)
     */
    fun isValidPinFormat(pin: String): Boolean {
        return pin.length >= 4 && pin.all { it.isDigit() }
    }
    
    /**
     * Checks if PIN is too weak (e.g., all same digits, sequential)
     */
    fun isPinWeak(pin: String): Boolean {
        // Check for all same digits
        if (pin.all { it == pin[0] }) {
            return true
        }
        
        // Check for sequential digits (ascending)
        var isSequential = true
        for (i in 1 until pin.length) {
            if (pin[i].digitToInt() != pin[i-1].digitToInt() + 1) {
                isSequential = false
                break
            }
        }
        if (isSequential) return true
        
        // Check for sequential digits (descending)
        isSequential = true
        for (i in 1 until pin.length) {
            if (pin[i].digitToInt() != pin[i-1].digitToInt() - 1) {
                isSequential = false
                break
            }
        }
        if (isSequential) return true
        
        // Check for common weak PINs
        val weakPins = setOf("1234", "0000", "1111", "2222", "3333", "4444", 
                            "5555", "6666", "7777", "8888", "9999", "1122", 
                            "1212", "2121", "4321", "0123")
        
        return weakPins.contains(pin)
    }
}
