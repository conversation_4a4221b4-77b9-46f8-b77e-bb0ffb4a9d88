package com.kidsphone.utils

import android.content.Context
import android.content.Intent
import android.content.pm.ApplicationInfo
import android.content.pm.PackageManager
import android.content.pm.ResolveInfo
import com.kidsphone.models.AppInfo

class AppUtils(private val context: Context) {
    
    private val packageManager = context.packageManager
    
    /**
     * Gets all installed apps that can be launched
     */
    fun getInstalledApps(): List<AppInfo> {
        val apps = mutableListOf<AppInfo>()
        
        // Get all apps with launcher intent
        val intent = Intent(Intent.ACTION_MAIN, null)
        intent.addCategory(Intent.CATEGORY_LAUNCHER)
        
        val resolveInfoList: List<ResolveInfo> = packageManager.queryIntentActivities(intent, 0)
        
        for (resolveInfo in resolveInfoList) {
            val packageName = resolveInfo.activityInfo.packageName
            
            // Skip our own app
            if (packageName == context.packageName) {
                continue
            }
            
            try {
                val applicationInfo = packageManager.getApplicationInfo(packageName, 0)
                val appName = packageManager.getApplicationLabel(applicationInfo).toString()
                val icon = packageManager.getApplicationIcon(applicationInfo)
                val isSystemApp = (applicationInfo.flags and ApplicationInfo.FLAG_SYSTEM) != 0
                
                apps.add(AppInfo(
                    packageName = packageName,
                    appName = appName,
                    icon = icon,
                    isSystemApp = isSystemApp
                ))
            } catch (e: PackageManager.NameNotFoundException) {
                // Skip apps that can't be found
                continue
            }
        }
        
        // Sort apps alphabetically by name
        return apps.sortedBy { it.appName.lowercase() }
    }
    
    /**
     * Gets user-installed apps only (non-system apps)
     */
    fun getUserApps(): List<AppInfo> {
        return getInstalledApps().filter { !it.isSystemApp }
    }
    
    /**
     * Gets essential system apps that might be needed for children
     */
    fun getEssentialSystemApps(): List<AppInfo> {
        val essentialPackages = setOf(
            "com.android.camera2",
            "com.android.camera",
            "com.google.android.apps.photos",
            "com.android.gallery3d",
            "com.android.calculator2",
            "com.google.android.calculator",
            "com.android.clock",
            "com.google.android.deskclock",
            "com.android.contacts",
            "com.google.android.contacts",
            "com.android.dialer",
            "com.google.android.dialer",
            "com.android.mms",
            "com.google.android.apps.messaging"
        )
        
        return getInstalledApps().filter { 
            it.isSystemApp && essentialPackages.contains(it.packageName) 
        }
    }
    
    /**
     * Gets recommended apps for children (educational, games, etc.)
     */
    fun getRecommendedApps(): List<AppInfo> {
        val recommendedPackages = setOf(
            "com.google.android.youtube.kids",
            "com.duolingo",
            "com.khanacademy.android",
            "com.google.android.apps.books",
            "com.spotify.music",
            "com.netflix.mediaclient",
            "com.disney.disneyplus"
        )
        
        return getInstalledApps().filter { 
            recommendedPackages.contains(it.packageName) 
        }
    }
    
    /**
     * Checks if an app is installed
     */
    fun isAppInstalled(packageName: String): Boolean {
        return try {
            packageManager.getApplicationInfo(packageName, 0)
            true
        } catch (e: PackageManager.NameNotFoundException) {
            false
        }
    }
    
    /**
     * Launches an app by package name
     */
    fun launchApp(packageName: String): Boolean {
        return try {
            val intent = packageManager.getLaunchIntentForPackage(packageName)
            if (intent != null) {
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                context.startActivity(intent)
                true
            } else {
                false
            }
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * Gets app name by package name
     */
    fun getAppName(packageName: String): String? {
        return try {
            val applicationInfo = packageManager.getApplicationInfo(packageName, 0)
            packageManager.getApplicationLabel(applicationInfo).toString()
        } catch (e: PackageManager.NameNotFoundException) {
            null
        }
    }
    
    /**
     * Filters apps based on search query
     */
    fun filterApps(apps: List<AppInfo>, query: String): List<AppInfo> {
        if (query.isBlank()) return apps
        
        val lowerQuery = query.lowercase()
        return apps.filter { 
            it.appName.lowercase().contains(lowerQuery) ||
            it.packageName.lowercase().contains(lowerQuery)
        }
    }
}
