package com.kidsphone.fragments.onboarding

import android.app.AppOpsManager
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.provider.Settings
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.activity.result.contract.ActivityResultContracts
import androidx.fragment.app.Fragment
import com.google.android.material.snackbar.Snackbar
import com.kidsphone.R
import com.kidsphone.activities.OnboardingActivity
import com.kidsphone.databinding.FragmentPermissionsBinding

class PermissionsFragment : Fragment() {
    
    private var _binding: FragmentPermissionsBinding? = null
    private val binding get() = _binding!!
    
    // Permission request launchers
    private val overlayPermissionLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) {
        updatePermissionStatus()
        (activity as? OnboardingActivity)?.refreshNavigationButtons()
    }
    
    private val usageStatsPermissionLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) {
        updatePermissionStatus()
        (activity as? OnboardingActivity)?.refreshNavigationButtons()
    }
    
    private val accessibilityPermissionLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) {
        updatePermissionStatus()
        (activity as? OnboardingActivity)?.refreshNavigationButtons()
    }
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentPermissionsBinding.inflate(inflater, container, false)
        return binding.root
    }
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        setupViews()
        updatePermissionStatus()
    }
    
    private fun setupViews() {
        // Usage Stats Permission
        binding.usageStatsCard.setOnClickListener {
            requestUsageStatsPermission()
        }
        
        binding.usageStatsButton.setOnClickListener {
            requestUsageStatsPermission()
        }
        
        // Accessibility Permission
        binding.accessibilityCard.setOnClickListener {
            requestAccessibilityPermission()
        }
        
        binding.accessibilityButton.setOnClickListener {
            requestAccessibilityPermission()
        }
        
        // Overlay Permission
        binding.overlayCard.setOnClickListener {
            requestOverlayPermission()
        }
        
        binding.overlayButton.setOnClickListener {
            requestOverlayPermission()
        }
        
        // Grant All Button
        binding.grantAllButton.setOnClickListener {
            grantAllPermissions()
        }
    }
    
    private fun requestUsageStatsPermission() {
        try {
            val intent = Intent(Settings.ACTION_USAGE_ACCESS_SETTINGS)
            usageStatsPermissionLauncher.launch(intent)
        } catch (e: Exception) {
            showError(getString(R.string.error_opening_settings))
        }
    }
    
    private fun requestAccessibilityPermission() {
        try {
            val intent = Intent(Settings.ACTION_ACCESSIBILITY_SETTINGS)
            accessibilityPermissionLauncher.launch(intent)
        } catch (e: Exception) {
            showError(getString(R.string.error_opening_settings))
        }
    }
    
    private fun requestOverlayPermission() {
        try {
            val intent = Intent(
                Settings.ACTION_MANAGE_OVERLAY_PERMISSION,
                Uri.parse("package:${requireContext().packageName}")
            )
            overlayPermissionLauncher.launch(intent)
        } catch (e: Exception) {
            showError(getString(R.string.error_opening_settings))
        }
    }
    
    private fun grantAllPermissions() {
        if (!hasUsageStatsPermission()) {
            requestUsageStatsPermission()
        } else if (!hasAccessibilityPermission()) {
            requestAccessibilityPermission()
        } else if (!hasOverlayPermission()) {
            requestOverlayPermission()
        }
    }
    
    private fun updatePermissionStatus() {
        // Usage Stats Permission
        val hasUsageStats = hasUsageStatsPermission()
        binding.usageStatsStatus.text = if (hasUsageStats) {
            getString(R.string.permission_granted)
        } else {
            getString(R.string.permission_required)
        }
        binding.usageStatsStatus.setTextColor(
            if (hasUsageStats) {
                resources.getColor(R.color.pin_success, null)
            } else {
                resources.getColor(R.color.pin_error, null)
            }
        )
        binding.usageStatsButton.text = if (hasUsageStats) {
            getString(R.string.granted)
        } else {
            getString(R.string.grant)
        }
        binding.usageStatsButton.isEnabled = !hasUsageStats
        
        // Accessibility Permission
        val hasAccessibility = hasAccessibilityPermission()
        binding.accessibilityStatus.text = if (hasAccessibility) {
            getString(R.string.permission_granted)
        } else {
            getString(R.string.permission_required)
        }
        binding.accessibilityStatus.setTextColor(
            if (hasAccessibility) {
                resources.getColor(R.color.pin_success, null)
            } else {
                resources.getColor(R.color.pin_error, null)
            }
        )
        binding.accessibilityButton.text = if (hasAccessibility) {
            getString(R.string.granted)
        } else {
            getString(R.string.grant)
        }
        binding.accessibilityButton.isEnabled = !hasAccessibility
        
        // Overlay Permission
        val hasOverlay = hasOverlayPermission()
        binding.overlayStatus.text = if (hasOverlay) {
            getString(R.string.permission_granted)
        } else {
            getString(R.string.permission_required)
        }
        binding.overlayStatus.setTextColor(
            if (hasOverlay) {
                resources.getColor(R.color.pin_success, null)
            } else {
                resources.getColor(R.color.pin_error, null)
            }
        )
        binding.overlayButton.text = if (hasOverlay) {
            getString(R.string.granted)
        } else {
            getString(R.string.grant)
        }
        binding.overlayButton.isEnabled = !hasOverlay
        
        // Update grant all button
        val allGranted = hasUsageStats && hasAccessibility && hasOverlay
        binding.grantAllButton.visibility = if (allGranted) View.GONE else View.VISIBLE
        
        // Update progress
        val grantedCount = listOf(hasUsageStats, hasAccessibility, hasOverlay).count { it }
        binding.progressIndicator.progress = (grantedCount * 100) / 3
        binding.progressText.text = getString(R.string.permissions_progress, grantedCount, 3)
    }
    
    private fun hasUsageStatsPermission(): Boolean {
        val appOpsManager = requireContext().getSystemService(Context.APP_OPS_SERVICE) as AppOpsManager
        val mode = appOpsManager.checkOpNoThrow(
            AppOpsManager.OPSTR_GET_USAGE_STATS,
            android.os.Process.myUid(),
            requireContext().packageName
        )
        return mode == AppOpsManager.MODE_ALLOWED
    }
    
    private fun hasAccessibilityPermission(): Boolean {
        // This is a simplified check - in a real implementation, you'd check if your
        // accessibility service is enabled
        return false // Placeholder - will be implemented with the accessibility service
    }
    
    private fun hasOverlayPermission(): Boolean {
        return Settings.canDrawOverlays(requireContext())
    }
    
    fun areAllPermissionsGranted(): Boolean {
        return hasUsageStatsPermission() && hasAccessibilityPermission() && hasOverlayPermission()
    }
    
    private fun showError(message: String) {
        Snackbar.make(binding.root, message, Snackbar.LENGTH_LONG).show()
    }
    
    override fun onResume() {
        super.onResume()
        updatePermissionStatus()
        (activity as? OnboardingActivity)?.refreshNavigationButtons()
    }
    
    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
