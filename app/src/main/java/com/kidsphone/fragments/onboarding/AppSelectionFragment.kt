package com.kidsphone.fragments.onboarding

import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.google.android.material.chip.Chip
import com.google.android.material.snackbar.Snackbar
import com.kidsphone.R
import com.kidsphone.activities.OnboardingActivity
import com.kidsphone.adapters.AppsAdapter
import com.kidsphone.databinding.FragmentAppSelectionBinding
import com.kidsphone.models.AppInfo
import com.kidsphone.utils.AppUtils
import com.kidsphone.utils.PreferencesManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class AppSelectionFragment : Fragment() {
    
    private var _binding: FragmentAppSelectionBinding? = null
    private val binding get() = _binding!!
    
    private lateinit var preferencesManager: PreferencesManager
    private lateinit var appUtils: AppUtils
    private lateinit var appsAdapter: AppsAdapter
    
    private var allApps = listOf<AppInfo>()
    private var filteredApps = listOf<AppInfo>()
    private var selectedApps = mutableSetOf<String>()
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentAppSelectionBinding.inflate(inflater, container, false)
        return binding.root
    }
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        preferencesManager = PreferencesManager(requireContext())
        appUtils = AppUtils(requireContext())
        
        setupViews()
        loadApps()
    }
    
    private fun setupViews() {
        // Setup RecyclerView
        appsAdapter = AppsAdapter(
            onAppClick = { app ->
                toggleAppSelection(app)
            },
            showCheckbox = true
        )
        
        binding.appsRecyclerView.apply {
            layoutManager = LinearLayoutManager(requireContext())
            adapter = appsAdapter
        }
        
        // Setup search
        binding.searchEditText.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
            override fun afterTextChanged(s: Editable?) {
                filterApps(s?.toString() ?: "")
            }
        })
        
        // Setup filter chips
        setupFilterChips()
        
        // Setup select all/none buttons
        binding.selectAllButton.setOnClickListener {
            selectAllVisibleApps()
        }
        
        binding.selectNoneButton.setOnClickListener {
            deselectAllApps()
        }
    }
    
    private fun setupFilterChips() {
        binding.chipAll.setOnCheckedChangeListener { _, isChecked ->
            if (isChecked) {
                showAllApps()
            }
        }
        
        binding.chipUserApps.setOnCheckedChangeListener { _, isChecked ->
            if (isChecked) {
                showUserApps()
            }
        }
        
        binding.chipSystemApps.setOnCheckedChangeListener { _, isChecked ->
            if (isChecked) {
                showSystemApps()
            }
        }
        
        binding.chipRecommended.setOnCheckedChangeListener { _, isChecked ->
            if (isChecked) {
                showRecommendedApps()
            }
        }
        
        // Default to all apps
        binding.chipAll.isChecked = true
    }
    
    private fun loadApps() {
        binding.progressBar.visibility = View.VISIBLE
        binding.appsRecyclerView.visibility = View.GONE
        
        lifecycleScope.launch {
            try {
                allApps = withContext(Dispatchers.IO) {
                    appUtils.getInstalledApps()
                }
                
                // Load previously selected apps
                selectedApps = preferencesManager.getAllowedApps().toMutableSet()
                
                // Mark selected apps
                allApps.forEach { app ->
                    app.isSelected = selectedApps.contains(app.packageName)
                }
                
                showAllApps()
                updateSelectedCount()
                
            } catch (e: Exception) {
                showError(getString(R.string.error_loading_apps))
            } finally {
                binding.progressBar.visibility = View.GONE
                binding.appsRecyclerView.visibility = View.VISIBLE
            }
        }
    }
    
    private fun showAllApps() {
        filteredApps = allApps
        appsAdapter.submitList(filteredApps)
    }
    
    private fun showUserApps() {
        filteredApps = allApps.filter { !it.isSystemApp }
        appsAdapter.submitList(filteredApps)
    }
    
    private fun showSystemApps() {
        filteredApps = allApps.filter { it.isSystemApp }
        appsAdapter.submitList(filteredApps)
    }
    
    private fun showRecommendedApps() {
        lifecycleScope.launch {
            val recommended = withContext(Dispatchers.IO) {
                appUtils.getRecommendedApps()
            }
            filteredApps = recommended
            appsAdapter.submitList(filteredApps)
        }
    }
    
    private fun filterApps(query: String) {
        val filtered = appUtils.filterApps(filteredApps, query)
        appsAdapter.submitList(filtered)
    }
    
    private fun toggleAppSelection(app: AppInfo) {
        if (app.isSelected) {
            selectedApps.add(app.packageName)
        } else {
            selectedApps.remove(app.packageName)
        }
        
        // Save to preferences
        preferencesManager.setAllowedApps(selectedApps)
        
        updateSelectedCount()
        
        // Notify parent activity to update navigation
        (activity as? OnboardingActivity)?.refreshNavigationButtons()
    }
    
    private fun selectAllVisibleApps() {
        appsAdapter.currentList.forEach { app ->
            if (!app.isSelected) {
                app.isSelected = true
                selectedApps.add(app.packageName)
            }
        }
        
        preferencesManager.setAllowedApps(selectedApps)
        appsAdapter.notifyDataSetChanged()
        updateSelectedCount()
        
        (activity as? OnboardingActivity)?.refreshNavigationButtons()
    }
    
    private fun deselectAllApps() {
        allApps.forEach { app ->
            app.isSelected = false
        }
        selectedApps.clear()
        
        preferencesManager.setAllowedApps(selectedApps)
        appsAdapter.notifyDataSetChanged()
        updateSelectedCount()
        
        (activity as? OnboardingActivity)?.refreshNavigationButtons()
    }
    
    private fun updateSelectedCount() {
        val count = selectedApps.size
        binding.selectedCountTextView.text = getString(R.string.selected_apps_count, count)
    }
    
    private fun showError(message: String) {
        Snackbar.make(binding.root, message, Snackbar.LENGTH_LONG).show()
    }
    
    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
