package com.kidsphone.fragments.onboarding

import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import com.google.android.material.snackbar.Snackbar
import com.kidsphone.R
import com.kidsphone.activities.OnboardingActivity
import com.kidsphone.databinding.FragmentPinSetupBinding
import com.kidsphone.utils.PreferencesManager
import com.kidsphone.utils.SecurityUtils

class PinSetupFragment : Fragment() {
    
    private var _binding: FragmentPinSetupBinding? = null
    private val binding get() = _binding!!
    
    private lateinit var preferencesManager: PreferencesManager
    private lateinit var securityUtils: SecurityUtils
    
    private var currentStep = STEP_ENTER_PIN
    private var enteredPin = ""
    
    companion object {
        private const val STEP_ENTER_PIN = 0
        private const val STEP_CONFIRM_PIN = 1
        private const val MIN_PIN_LENGTH = 4
    }
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentPinSetupBinding.inflate(inflater, container, false)
        return binding.root
    }
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        preferencesManager = PreferencesManager(requireContext())
        securityUtils = SecurityUtils()
        
        setupViews()
        setupPinInput()
    }
    
    private fun setupViews() {
        updateUI()
        
        binding.clearButton.setOnClickListener {
            clearPin()
        }
        
        // Setup number pad
        setupNumberPad()
    }
    
    private fun setupNumberPad() {
        val numberButtons = listOf(
            binding.button0, binding.button1, binding.button2, binding.button3,
            binding.button4, binding.button5, binding.button6, binding.button7,
            binding.button8, binding.button9
        )
        
        numberButtons.forEachIndexed { index, button ->
            button.setOnClickListener {
                addDigit(index.toString())
            }
        }
        
        binding.backspaceButton.setOnClickListener {
            removeLastDigit()
        }
    }
    
    private fun setupPinInput() {
        binding.pinEditText.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
            
            override fun afterTextChanged(s: Editable?) {
                val pin = s?.toString() ?: ""
                updatePinDots(pin.length)
                
                if (pin.length >= MIN_PIN_LENGTH) {
                    handlePinInput(pin)
                }
            }
        })
    }
    
    private fun addDigit(digit: String) {
        val currentPin = binding.pinEditText.text.toString()
        if (currentPin.length < 6) { // Max 6 digits
            binding.pinEditText.setText(currentPin + digit)
        }
    }
    
    private fun removeLastDigit() {
        val currentPin = binding.pinEditText.text.toString()
        if (currentPin.isNotEmpty()) {
            binding.pinEditText.setText(currentPin.dropLast(1))
        }
    }
    
    private fun clearPin() {
        binding.pinEditText.setText("")
        if (currentStep == STEP_CONFIRM_PIN) {
            currentStep = STEP_ENTER_PIN
            enteredPin = ""
            updateUI()
        }
    }
    
    private fun updatePinDots(length: Int) {
        val dots = listOf(
            binding.dot1, binding.dot2, binding.dot3,
            binding.dot4, binding.dot5, binding.dot6
        )
        
        dots.forEachIndexed { index, dot ->
            dot.isSelected = index < length
        }
    }
    
    private fun handlePinInput(pin: String) {
        when (currentStep) {
            STEP_ENTER_PIN -> {
                if (pin.length >= MIN_PIN_LENGTH) {
                    enteredPin = pin
                    currentStep = STEP_CONFIRM_PIN
                    binding.pinEditText.setText("")
                    updateUI()
                }
            }
            STEP_CONFIRM_PIN -> {
                if (pin == enteredPin) {
                    savePin(pin)
                } else {
                    showError(getString(R.string.pin_mismatch))
                    currentStep = STEP_ENTER_PIN
                    enteredPin = ""
                    binding.pinEditText.setText("")
                    updateUI()
                }
            }
        }
    }
    
    private fun savePin(pin: String) {
        try {
            val salt = securityUtils.generateSalt()
            val hash = securityUtils.hashPin(pin, salt)
            
            preferencesManager.setPinHash(hash, salt)
            
            showSuccess(getString(R.string.pin_setup_success))
            
            // Notify parent activity to update navigation
            (activity as? OnboardingActivity)?.refreshNavigationButtons()
            
        } catch (e: Exception) {
            showError(getString(R.string.error_generic))
        }
    }
    
    private fun updateUI() {
        when (currentStep) {
            STEP_ENTER_PIN -> {
                binding.titleTextView.text = getString(R.string.setup_pin_title)
                binding.descriptionTextView.text = getString(R.string.setup_pin_description)
                binding.instructionTextView.text = getString(R.string.enter_pin)
            }
            STEP_CONFIRM_PIN -> {
                binding.titleTextView.text = getString(R.string.setup_pin_title)
                binding.descriptionTextView.text = getString(R.string.setup_pin_description)
                binding.instructionTextView.text = getString(R.string.confirm_pin)
            }
        }
    }
    
    private fun showError(message: String) {
        Snackbar.make(binding.root, message, Snackbar.LENGTH_LONG)
            .setBackgroundTint(resources.getColor(R.color.pin_error, null))
            .show()
    }
    
    private fun showSuccess(message: String) {
        Snackbar.make(binding.root, message, Snackbar.LENGTH_SHORT)
            .setBackgroundTint(resources.getColor(R.color.pin_success, null))
            .show()
    }
    
    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
