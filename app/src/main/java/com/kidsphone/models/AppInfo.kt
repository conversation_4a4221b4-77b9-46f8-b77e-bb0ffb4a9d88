package com.kidsphone.models

import android.graphics.drawable.Drawable

data class AppInfo(
    val packageName: String,
    val appName: String,
    val icon: Drawable?,
    val isSystemApp: Boolean = false,
    var isSelected: Boolean = false
) {
    override fun equals(other: Any?): Bo<PERSON>an {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false
        
        other as AppInfo
        
        return packageName == other.packageName
    }
    
    override fun hashCode(): Int {
        return packageName.hashCode()
    }
}
